from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status, Request, Response
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
import os
import json
import base64
import tempfile
import time
from datetime import datetime, timedelta
from urllib.parse import urlencode
import re
import hashlib
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow, Flow
from google.auth.transport.requests import Request as GoogleRequest
from google.oauth2.credentials import Credentials
from firebase_admin import firestore, storage, auth
from routers.auth_router import verify_token
import email_storage
import logging

# Import the Flanker email parser
from email_parser import parse_email_with_flanker, parse_gmail_api_message, is_valid_attachment_id

# Import the new OAuth configuration
from oauth_config import get_oauth_config

# Import the OAuth utilities
from utils.oauth_utils import get_oauth_scopes, validate_oauth_response, generate_oauth_redirect_url

# Configure logging
logger = logging.getLogger("gmail_router")

# Get OAuth configuration
oauth_config = get_oauth_config()
CLIENT_ID = oauth_config["client_id"]
CLIENT_SECRET = oauth_config["client_secret"]
REDIRECT_URI = oauth_config["redirect_uri"]
# Define Gmail API scopes
SCOPES = [
    'openid',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/gmail.readonly'
]
CREDENTIALS_FILE_PATH = oauth_config["credentials_file"]

# Define the router
router = APIRouter()

# Path to the credentials JSON file (can be set at runtime)
CREDENTIALS_FILE_PATH = None

# Define Gmail API scopes
# Define Gmail API scopes
SCOPES = [
    'openid',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/gmail.readonly'
]

# Rate limiting configuration
MAX_REQUESTS_PER_MINUTE = 60  # Gmail API has a limit of 1 request per second per user
REQUEST_WINDOW = 60  # 60 seconds window

# Track requests for rate limiting
user_request_tracker = {}

# Models with improved validation
class GmailCredentials(BaseModel):
    token: Dict[str, Any]
    refresh_token: str
    token_uri: str
    client_id: str
    client_secret: str
    scopes: List[str]

class GmailCredentialsFile(BaseModel):
    file_path: str

class EmailSearchQuery(BaseModel):
    query: str
    max_results: Optional[int] = Field(default=50, ge=1, le=100)
    account_id: Optional[str] = Field(default="primary")

class EmailDateSearch(BaseModel):
    date: str  # Format: YYYY-MM-DD
    account_id: Optional[str] = Field(default="primary")

class CredentialsFilePath(BaseModel):
    file_path: str

class BatchAnalyzeRequest(BaseModel):
    query: str
    max_results: Optional[int] = Field(default=50, ge=1, le=100)
    date: Optional[str] = None  # Format: YYYY-MM-DD
    start_date: Optional[str] = None  # Format: YYYY-MM-DD
    end_date: Optional[str] = None  # Format: YYYY-MM-DD
    use_last_retrieval_time: Optional[bool] = Field(default=True)
    account_id: Optional[str] = Field(default="primary")

class AccountRequest(BaseModel):
    accountId: Optional[str] = Field(default="primary")

def check_rate_limit(user_id: str) -> bool:
    """
    Check if the user has exceeded the rate limit
    Returns True if request should proceed, False if rate limited
    """
    current_time = time.time()

    if user_id not in user_request_tracker:
        user_request_tracker[user_id] = []

    # Remove requests older than the window
    user_request_tracker[user_id] = [t for t in user_request_tracker[user_id]
                                    if current_time - t < REQUEST_WINDOW]

    # Check if user has exceeded the limit
    if len(user_request_tracker[user_id]) >= MAX_REQUESTS_PER_MINUTE:
        return False

    # Add current request timestamp
    user_request_tracker[user_id].append(current_time)
    return True

def get_gmail_service(credentials_dict=None, credentials_file=None, user_id=None, account_id=None):
    """Build and return Gmail API service from credentials dict or JSON file with automatic refresh"""
    creds = None

    if credentials_file:
        try:
            with open(credentials_file, 'r') as file:
                creds_data = json.load(file)
                creds = Credentials.from_authorized_user_info(info=creds_data, scopes=SCOPES)
        except Exception as e:
            logger.error(f"Error loading credentials from file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error loading credentials from file: {str(e)}"
            )
    elif credentials_dict:
        creds = Credentials.from_authorized_user_info(info=credentials_dict, scopes=SCOPES)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No credentials provided"
        )

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                logger.info(f"Refreshing expired credentials for account {account_id}")
                creds.refresh(GoogleRequest())

                # Update the refreshed credentials in Firestore
                if user_id and account_id:
                    try:
                        update_credentials_in_firestore(user_id, account_id, creds)
                        logger.info(f"Updated refreshed credentials in Firestore for account {account_id}")
                    except Exception as update_err:
                        logger.error(f"Error updating refreshed credentials in Firestore: {str(update_err)}")
                        # Don't fail the request if we can't update Firestore

            except Exception as e:
                logger.error(f"Error refreshing credentials: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Error refreshing credentials: {str(e)}. Please re-authenticate your Gmail account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Gmail credentials. Please re-authenticate your Gmail account."
            )

    return build('gmail', 'v1', credentials=creds)

def update_credentials_in_firestore(user_id, account_id, credentials):
    """Update refreshed credentials in Firestore"""
    try:
        db = firestore.client()

        credentials_dict = {
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret,
            'scopes': credentials.scopes
        }

        if account_id == "primary":
            # Update primary account in user document
            db.collection('users').document(user_id).update({
                'gmail_credentials': credentials_dict,
                'updated_at': firestore.SERVER_TIMESTAMP
            })

            # Also update in email_accounts collection
            db.collection('users').document(user_id).collection('email_accounts').document('primary').update({
                'credentials': credentials_dict,
                'lastSynced': datetime.now().isoformat(),
                'updated_at': firestore.SERVER_TIMESTAMP
            })
        else:
            # Update specific account
            db.collection('users').document(user_id).collection('email_accounts').document(account_id).update({
                'credentials': credentials_dict,
                'lastSynced': datetime.now().isoformat(),
                'updated_at': firestore.SERVER_TIMESTAMP
            })

        logger.info(f"Successfully updated credentials in Firestore for account {account_id}")
    except Exception as e:
        logger.error(f"Error updating credentials in Firestore: {str(e)}")
        raise

def search_messages(service, user_id='me', search_string='', max_results=None):
    """Search for messages in Gmail with optimized rate limiting"""
    try:
        # Initialize request parameters
        params = {
            'userId': user_id,
            'q': search_string
        }

        # Only add maxResults if a limit is specified
        if max_results is not None:
            params['maxResults'] = max_results

        # Execute the initial request
        search_id = service.users().messages().list(**params).execute()

        message_ids = []
        if 'messages' in search_id:
            message_ids.extend([msg['id'] for msg in search_id['messages']])

            # If no limit is specified, continue fetching all pages of results
            if max_results is None and 'nextPageToken' in search_id:
                page_token = search_id['nextPageToken']
                page_count = 1

                # Limit the number of pages to prevent excessive API calls
                max_pages = 10

                while page_token and page_count < max_pages:
                    # Add the page token to params
                    params['pageToken'] = page_token

                    # Rate limiting - sleep briefly between requests
                    time.sleep(0.2)  # 200ms delay to avoid rate limits

                    # Get the next page of results
                    next_page = service.users().messages().list(**params).execute()

                    # Add the message IDs from this page
                    if 'messages' in next_page:
                        message_ids.extend([msg['id'] for msg in next_page['messages']])

                    # Update the page token for the next iteration
                    page_token = next_page.get('nextPageToken')

                    # Increment page count
                    page_count += 1

                    if not page_token:
                        break

        return message_ids
    except Exception as e:
        logger.error(f"Error searching messages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching messages: {str(e)}"
        )

def get_raw_email_data(service, user_id='me', msg_id=''):
    """Get raw email data for Flanker parsing"""
    try:
        raw_message = service.users().messages().get(
            userId=user_id, id=msg_id, format='raw'
        ).execute()

        if 'raw' in raw_message:
            raw_data = base64.urlsafe_b64decode(raw_message['raw'])
            return raw_data, raw_message
        return None, raw_message
    except Exception as e:
        logger.warning(f"Error getting raw email data: {str(e)}")
        return None, None

def get_message_details(service, user_id='me', msg_id=''):
    """Get details of a specific email message with enhanced error handling, prioritizing Gmail API for attachments"""
    try:
        # Get the full message from Gmail API first
        message = service.users().messages().get(
            userId=user_id, id=msg_id, format='full'
        ).execute()

        headers = message['payload']['headers']

        # Extract email details
        subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
        from_email = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown Sender')
        to_email = next((h['value'] for h in headers if h['name'].lower() == 'to'), '')
        cc_email = next((h['value'] for h in headers if h['name'].lower() == 'cc'), '')
        bcc_email = next((h['value'] for h in headers if h['name'].lower() == 'bcc'), '')
        date = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown Date')

        # Get message labels
        labels = message.get('labelIds', [])

        # Get message body and attachments directly from Gmail API
        body, attachments = get_message_body_and_attachments(service, user_id, message)

        # Extract a snippet if available
        snippet = message.get('snippet', '')

        # Create a dictionary with standardized fields
        result = {
            'id': msg_id,
            'threadId': message.get('threadId', ''),
            'subject': subject,
            'from': from_email,
            'to': to_email,
            'cc': cc_email,
            'bcc': bcc_email,
            'date': date,
            'body': body,
            'snippet': snippet,
            'attachments': attachments,
            'labels': labels
        }

        # Try to get the raw message for additional parsing if needed
        try:
            raw_data, raw_message = get_raw_email_data(service, user_id, msg_id)
            if raw_data:
                # Add raw data for storage
                result['raw_data'] = raw_data

                # Try to parse with Flanker for additional metadata
                flanker_result = parse_email_with_flanker(raw_data)
                if flanker_result:
                    logger.info(f"Successfully parsed email {msg_id} with Flanker for additional metadata")

                    # Add any additional metadata from Flanker that's not already in the result
                    for key, value in flanker_result.items():
                        if key not in result and key != 'attachments':
                            result[key] = value
        except Exception as flanker_err:
            logger.warning(f"Flanker parsing failed for email {msg_id}: {str(flanker_err)}. Using only Gmail API data.")

        return result
    except Exception as e:
        logger.error(f"Error getting message details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting message details: {str(e)}"
        )

def get_message_body_and_attachments(service, user_id, message):
    """Extract the body and attachments from a message with improved handling"""
    body = {"html": "", "text": ""}
    attachments = []

    # Helper function to process parts recursively
    def process_parts(parts, parent_part=None):
        for part in parts:
            mime_type = part.get('mimeType', 'text/plain')
            part_body = part.get('body', {})

            # Handle different part types
            if mime_type == 'text/plain' and 'data' in part_body:
                try:
                    text_content = base64.urlsafe_b64decode(part_body['data']).decode('utf-8', errors='replace')
                    body["text"] = text_content
                except Exception as e:
                    logger.warning(f"Error decoding text content: {str(e)}")

            elif mime_type == 'text/html' and 'data' in part_body:
                try:
                    html_content = base64.urlsafe_b64decode(part_body['data']).decode('utf-8', errors='replace')
                    body["html"] = html_content
                except Exception as e:
                    logger.warning(f"Error decoding HTML content: {str(e)}")

            # Handle attachments
            elif 'filename' in part and part['filename']:
                attachment = {
                    'id': part_body.get('attachmentId', ''),
                    'filename': part['filename'],
                    'mimeType': mime_type,
                    'size': part_body.get('size', 0)
                }
                attachments.append(attachment)

            # Process nested parts if they exist
            if 'parts' in part:
                process_parts(part['parts'], part)

    # Start processing from the message payload
    if 'parts' in message['payload']:
        process_parts(message['payload']['parts'])
    elif 'body' in message['payload'] and 'data' in message['payload']['body']:
        # Handle single-part messages
        try:
            if message['payload']['mimeType'] == 'text/html':
                body["html"] = base64.urlsafe_b64decode(
                    message['payload']['body']['data']
                ).decode('utf-8', errors='replace')
            else:
                body["text"] = base64.urlsafe_b64decode(
                    message['payload']['body']['data']
                ).decode('utf-8', errors='replace')
        except Exception as e:
            logger.warning(f"Error decoding message body: {str(e)}")

    # If we haven't found any content, check for body data at the top level
    if not body["text"] and not body["html"] and 'body' in message and 'data' in message['body']:
        try:
            body["text"] = base64.urlsafe_b64decode(
                message['body']['data']
            ).decode('utf-8', errors='replace')
        except Exception as e:
            logger.warning(f"Error decoding top-level body: {str(e)}")

    return body, attachments

def download_attachment(service, user_id, message_id, attachment_id, filename, firebase_user_id):
    """Download an attachment and upload it to Firebase Storage using user_id in the path"""
    try:
        logger.info(f"Downloading attachment: {filename} from email {message_id} with attachment ID {attachment_id}")

        # Get the attachment data from Gmail API
        try:
            attachment = service.users().messages().attachments().get(
                userId=user_id, messageId=message_id, id=attachment_id
            ).execute()
        except Exception as e:
            logger.error(f"Error getting attachment from Gmail API: {str(e)}")
            raise

        # Decode the attachment data
        try:
            file_data = base64.urlsafe_b64decode(attachment['data'])
            logger.info(f"Successfully decoded attachment data, size: {len(file_data)} bytes")
        except Exception as e:
            logger.error(f"Error decoding attachment data: {str(e)}")
            raise

        # Sanitize the filename to avoid issues with special characters
        safe_filename = ''.join(c for c in filename if c.isalnum() or c in '._- ')
        if safe_filename != filename:
            logger.info(f"Sanitized filename from '{filename}' to '{safe_filename}'")

        # Create a temporary file for upload
        try:
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name
            logger.info(f"Wrote attachment to temporary file: {temp_file_path}")
        except Exception as e:
            logger.error(f"Error writing to temporary file: {str(e)}")
            raise

        # Upload to Firebase Storage using user_id in the path
        try:
            bucket = storage.bucket()
            storage_path = f"users/{firebase_user_id}/attachments/{message_id}/{safe_filename}"
            blob = bucket.blob(storage_path)

            # Set content type based on file extension
            extension = os.path.splitext(safe_filename)[1].lower()
            content_type = None
            if extension == '.pdf':
                content_type = 'application/pdf'
            elif extension == '.doc' or extension == '.docx':
                content_type = 'application/msword'
            elif extension == '.xls' or extension == '.xlsx':
                content_type = 'application/vnd.ms-excel'
            elif extension == '.jpg' or extension == '.jpeg':
                content_type = 'image/jpeg'
            elif extension == '.png':
                content_type = 'image/png'
            elif extension == '.eml':
                content_type = 'message/rfc822'

            # Upload with content type if available
            if content_type:
                blob.upload_from_filename(temp_file_path, content_type=content_type)
            else:
                blob.upload_from_filename(temp_file_path)

            logger.info(f"Uploaded attachment to Firebase Storage: {storage_path}")

            # Get the download URL directly - this works with uniform bucket-level access
            # No need to make the blob public
            url = f"https://firebasestorage.googleapis.com/v0/b/{bucket.name}/o/{storage_path.replace('/', '%2F')}?alt=media"
            logger.info(f"Generated download URL for attachment: {url}")

        except Exception as e:
            logger.error(f"Error uploading to Firebase Storage: {str(e)}")
            raise
        finally:
            # Delete the temporary file
            try:
                os.unlink(temp_file_path)
                logger.info(f"Deleted temporary file: {temp_file_path}")
            except Exception as e:
                logger.error(f"Error deleting temporary file: {str(e)}")

        return url
    except Exception as e:
        logger.error(f"Error in download_attachment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error downloading attachment: {str(e)}"
        )

def get_date_search_query(selected_date):
    """Generate a search query for emails on a specific date"""
    date_obj = datetime.strptime(selected_date, '%Y-%m-%d')
    after_date = date_obj.strftime('%Y/%m/%d')
    before_date = (date_obj + timedelta(days=1)).strftime('%Y/%m/%d')
    return f"after:{after_date} before:{before_date}"

def get_attachment_data_direct(service, message_id, attachment_id):
    """Get attachment data directly without saving to Firebase Storage"""
    try:
        # Get attachment data from Gmail API
        attachment = service.users().messages().attachments().get(
            userId='me', messageId=message_id, id=attachment_id
        ).execute()

        # Decode the data
        file_data = base64.urlsafe_b64decode(attachment['data'])

        return file_data
    except Exception as e:
        logger.error(f"Error getting attachment data directly: {str(e)}")
        raise

def get_credentials_for_account(user_id, account_id):
    """Get Gmail credentials for a specific account"""
    try:
        logger.info(f"Getting credentials for user {user_id}, account {account_id}")
        db = firestore.client()

        if account_id == "primary":
            # Get credentials from user document
            user_doc = db.collection('users').document(user_id).get()

            if not user_doc.exists:
                logger.error(f"User document not found for user {user_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            user_data = user_doc.to_dict()
            if 'gmail_credentials' not in user_data:
                logger.error(f"Gmail credentials not found in user document for user {user_id}")

                # Check if user has any accounts in the email_accounts collection
                accounts_ref = db.collection('users').document(user_id).collection('email_accounts')
                accounts = list(accounts_ref.limit(1).get())

                if accounts:
                    # Use the first account as primary
                    first_account = accounts[0].to_dict()
                    if 'credentials' in first_account:
                        logger.info(f"Using first account as primary for user {user_id}")
                        return first_account['credentials']

                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Gmail credentials not found. Please connect your Gmail account."
                )

            logger.info(f"Found primary account credentials for user {user_id}")
            return user_data['gmail_credentials']
        else:
            # Get credentials from email_accounts collection
            account_doc = db.collection('users').document(user_id).collection('email_accounts').document(account_id).get()

            if not account_doc.exists:
                logger.error(f"Account {account_id} not found for user {user_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Account {account_id} not found"
                )

            account_data = account_doc.to_dict()
            if 'credentials' not in account_data:
                logger.error(f"Credentials not found in account {account_id} for user {user_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Credentials not found for account {account_id}"
                )

            logger.info(f"Found credentials for account {account_id}, user {user_id}")
            return account_data['credentials']
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting credentials for account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting credentials for account: {str(e)}"
        )

# OAuth routes
@router.get("/authorize")
async def authorize_gmail(request: Request):
    """Start the OAuth flow for Gmail API access"""
    try:
        logger.info("Starting OAuth flow")
        # Create the flow using client secrets file
        client_secrets_file = os.getenv("GMAIL_CLIENT_SECRETS_FILE") or os.getenv("GMAIL_CREDENTIALS_JSON_PATH")
        if not client_secrets_file:
            logger.error("Gmail client secrets file not configured")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Gmail client secrets file not configured"
            )

        logger.info(f"Using client secrets file: {client_secrets_file}")
        redirect_uri = os.getenv("GMAIL_REDIRECT_URI")
        logger.info(f"Using redirect URI: {redirect_uri}")

        flow = Flow.from_client_secrets_file(
            client_secrets_file,
            scopes=SCOPES,
            redirect_uri=redirect_uri
        )

        # Get user_id from query parameters if available
        user_id = request.query_params.get('user_id')

        # Check if this is a new account being added
        is_additional_account = request.query_params.get('additional') == 'true'

        # Generate the authorization URL
        authorization_url, state = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )

        # Store additional data with the state
        state_data = {'state': state}
        if user_id:
            state_data['user_id'] = user_id
            logger.info(f"Including user_id in state data: {user_id}")

        if is_additional_account:
            state_data['additional_account'] = True
            logger.info("This is an additional account being added")

        logger.info(f"Generated authorization URL: {authorization_url}")
        logger.info(f"Generated state: {state}")

        # Store the state in a more secure way using Firestore
        if user_id:
            try:
                db = firestore.client()
                db.collection('oauth_states').document(state).set({
                    'user_id': user_id,
                    'is_additional_account': is_additional_account,
                    'created_at': firestore.SERVER_TIMESTAMP,
                    'expires_at': datetime.now() + timedelta(minutes=15)  # 15 min expiration
                })
                logger.info(f"Saved OAuth state to Firestore for user {user_id}")
            except Exception as e:
                logger.error(f"Error saving OAuth state to Firestore: {str(e)}")

        # For backward compatibility, also save to file
        with open('oauth_state.json', 'w') as f:
            json.dump(state_data, f)
            logger.info(f"State data saved to oauth_state.json: {state_data}")

        # Redirect the user to the authorization URL
        return RedirectResponse(url=authorization_url)
    except Exception as e:
        logger.error(f"Error in authorize_gmail: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting OAuth flow: {str(e)}"
        )

@router.get("/callback")
async def oauth_callback(request: Request, code: str, state: str):
    """Handle the OAuth callback from Google"""
    try:
        logger.info("OAuth callback received with code and state")

        # Try to get state data from Firestore first
        user_id = None
        is_additional_account = False

        try:
            db = firestore.client()
            state_doc = db.collection('oauth_states').document(state).get()

            if state_doc.exists:
                state_data = state_doc.to_dict()
                user_id = state_data.get('user_id')
                is_additional_account = state_data.get('is_additional_account', False)
                logger.info(f"Retrieved OAuth state from Firestore: user_id={user_id}, is_additional_account={is_additional_account}")

                # Check if the state has expired
                expires_at = state_data.get('expires_at')
                if expires_at and expires_at.timestamp() < datetime.now().timestamp():
                    logger.error("OAuth state has expired")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="OAuth session expired"
                    )
            else:
                # Fall back to file-based state
                logger.info("OAuth state not found in Firestore, checking file")
                with open('oauth_state.json', 'r') as f:
                    state_data = json.load(f)
                    stored_state = state_data.get('state')

                    if stored_state != state:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Invalid state parameter"
                        )

                    user_id = state_data.get('user_id')
                    is_additional_account = state_data.get('additional_account', False)
                    logger.info(f"Retrieved OAuth state from file: user_id={user_id}, is_additional_account={is_additional_account}")
        except FileNotFoundError:
            logger.error("OAuth state file not found")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OAuth session expired"
            )

        # Create the flow using client secrets file
        client_secrets_file = os.getenv("GMAIL_CLIENT_SECRETS_FILE") or os.getenv("GMAIL_CREDENTIALS_JSON_PATH")
        logger.info(f"Using client secrets file: {client_secrets_file}")

        flow = Flow.from_client_secrets_file(
            client_secrets_file,
            scopes=SCOPES,
            redirect_uri=os.getenv("GMAIL_REDIRECT_URI"),
            state=state
        )

        # Exchange the authorization code for credentials
        logger.info("Exchanging authorization code for credentials")
        flow.fetch_token(code=code)
        credentials = flow.credentials
        logger.info(f"Received credentials with refresh token: {bool(credentials.refresh_token)}")

        # Always store credentials in a debug file
        with open('debug_credentials.json', 'w') as f:
            creds_dict = {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }
            json.dump(creds_dict, f)
            logger.info("Credentials saved to debug_credentials.json")

        # Set global credentials path for immediate use
        global CREDENTIALS_FILE_PATH
        CREDENTIALS_FILE_PATH = os.path.abspath('debug_credentials.json')
        logger.info(f"Global credentials path set to: {CREDENTIALS_FILE_PATH}")

        # Get Gmail service to get email address
        service = build('gmail', 'v1', credentials=credentials)
        profile = service.users().getProfile(userId='me').execute()
        email_address = profile.get('emailAddress', 'Unknown Email')

        # Store in Firestore with improved handling
        try:
            # Initialize Firestore client
            db = firestore.client()
            logger.info("Firestore client initialized successfully")

            # Try to extract user ID from Firebase token
            extracted_user_id = None
            if 'firebaseToken' in request.cookies:
                token = request.cookies['firebaseToken']
                try:
                    # Verify the token with Firebase Auth
                    decoded_token = auth.verify_id_token(token)
                    extracted_user_id = decoded_token["uid"]
                    logger.info(f"Successfully decoded token, user ID: {extracted_user_id}")
                except Exception as auth_err:
                    logger.error(f"Error authenticating with token: {auth_err}")

            # Use the user ID from state data if token extraction failed
            final_user_id = extracted_user_id or user_id

            if not final_user_id:
                logger.error("No user ID found in token or state data")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No user ID found"
                )

            logger.info(f"Using user ID: {final_user_id}")

            # Check if this is an additional account
            if is_additional_account:
                # Check for existing accounts with the same email
                accounts_ref = db.collection('users').document(final_user_id).collection('email_accounts')
                existing_accounts = accounts_ref.where('email', '==', email_address).get()

                if len(existing_accounts) > 0:
                    # Update existing account with the same email
                    existing_account = existing_accounts[0]
                    existing_account_id = existing_account.id

                    accounts_ref.document(existing_account_id).update({
                        'credentials': {
                            'token': credentials.token,
                            'refresh_token': credentials.refresh_token,
                            'token_uri': credentials.token_uri,
                            'client_id': credentials.client_id,
                            'client_secret': credentials.client_secret,
                            'scopes': credentials.scopes
                        },
                        'lastSynced': datetime.now().isoformat(),
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                    logger.info(f"Updated existing account for {email_address}")
                else:
                    # Account ID for a new account
                    account_id = f"gmail-{datetime.now().strftime('%Y%m%d%H%M%S')}"

                    # Add as a new account
                    accounts_ref.document(account_id).set({
                        'credentials': {
                            'token': credentials.token,
                            'refresh_token': credentials.refresh_token,
                            'token_uri': credentials.token_uri,
                            'client_id': credentials.client_id,
                            'client_secret': credentials.client_secret,
                            'scopes': credentials.scopes
                        },
                        'email': email_address,
                        'provider': 'Gmail',
                        'accountId': account_id,
                        'primary': False,
                        'lastSynced': datetime.now().isoformat(),
                        'created_at': firestore.SERVER_TIMESTAMP
                    })
                    logger.info(f"Added new account for {email_address}")
            else:
                # Store as the primary account (legacy method)
                db.collection('users').document(final_user_id).set({
                    'gmail_credentials': {
                        'token': credentials.token,
                        'refresh_token': credentials.refresh_token,
                        'token_uri': credentials.token_uri,
                        'client_id': credentials.client_id,
                        'client_secret': credentials.client_secret,
                        'scopes': credentials.scopes
                    },
                    'primary_email': email_address,
                    'updated_at': firestore.SERVER_TIMESTAMP,
                    'email_connected': True
                }, merge=True)
                logger.info(f"Credentials successfully stored for primary account {email_address}")

                # Also store in email_accounts collection for consistency
                accounts_ref = db.collection('users').document(final_user_id).collection('email_accounts')
                primary_account_doc = accounts_ref.document('primary').get()

                if primary_account_doc.exists:
                    # Update existing primary account
                    accounts_ref.document('primary').update({
                        'credentials': {
                            'token': credentials.token,
                            'refresh_token': credentials.refresh_token,
                            'token_uri': credentials.token_uri,
                            'client_id': credentials.client_id,
                            'client_secret': credentials.client_secret,
                            'scopes': credentials.scopes
                        },
                        'email': email_address,
                        'lastSynced': datetime.now().isoformat(),
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                else:
                    # Create primary account in email_accounts collection
                    accounts_ref.document('primary').set({
                        'credentials': {
                            'token': credentials.token,
                            'refresh_token': credentials.refresh_token,
                            'token_uri': credentials.token_uri,
                            'client_id': credentials.client_id,
                            'client_secret': credentials.client_secret,
                            'scopes': credentials.scopes
                        },
                        'email': email_address,
                        'provider': 'Gmail',
                        'accountId': 'primary',
                        'primary': True,
                        'lastSynced': datetime.now().isoformat(),
                        'created_at': firestore.SERVER_TIMESTAMP
                    })
                logger.info(f"Primary account also stored in email_accounts collection")

            # Clean up OAuth state from Firestore
            try:
                db.collection('oauth_states').document(state).delete()
                logger.info(f"Deleted OAuth state from Firestore")
            except Exception as e:
                logger.error(f"Error deleting OAuth state from Firestore: {str(e)}")

        except Exception as db_err:
            logger.error(f"Critical error with Firestore: {str(db_err)}")

        # Redirect back to the frontend dashboard with success parameter
        redirect_url = os.getenv("FRONTEND_URL", "https://ai-email-bot-455814.web.app")
        return RedirectResponse(url=f"{redirect_url}/dashboard?oauth=success", status_code=303)
    except Exception as e:
        logger.error(f"Error completing OAuth flow: {str(e)}")
        redirect_url = os.getenv("FRONTEND_URL", "https://ai-email-bot-455814.web.app")
        return RedirectResponse(url=f"{redirect_url}/dashboard?oauth=error&message={str(e)}", status_code=303)

@router.post("/set-credentials-file")
async def set_credentials_file_path(
    credentials_file: GmailCredentialsFile
):
    """Set the path to the Gmail API credentials JSON file"""
    global CREDENTIALS_FILE_PATH
    try:
        # Validate that the file exists and is valid JSON
        with open(credentials_file.file_path, 'r') as file:
            creds_data = json.load(file)

            # Validate the credentials format
            if not all(key in creds_data for key in ['token', 'refresh_token', 'token_uri', 'client_id', 'client_secret', 'scopes']):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid credentials format in the JSON file"
                )

        CREDENTIALS_FILE_PATH = credentials_file.file_path
        return {"message": f"Credentials file path set to: {CREDENTIALS_FILE_PATH}"}
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Credentials file not found at: {credentials_file.file_path}"
        )
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON format in the credentials file"
        )
    except Exception as e:
        logger.error(f"Error setting credentials file path: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error setting credentials file path: {str(e)}"
        )

@router.get("/credentials-file")
async def get_credentials_file_path():
    """Get the currently set credentials file path"""
    global CREDENTIALS_FILE_PATH
    if CREDENTIALS_FILE_PATH:
        return {"credentials_file": CREDENTIALS_FILE_PATH}
    else:
        return {"credentials_file": None, "message": "No credentials file path has been set"}

@router.post("/store-credentials")
async def store_gmail_credentials(
    credentials: GmailCredentials,
    user_data: dict = Depends(verify_token)
):
    """Store Gmail API credentials in Firestore"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        db = firestore.client()

        # Store both in legacy location and in email_accounts collection
        db.collection('users').document(user_data['uid']).set({
            'gmail_credentials': credentials.dict(),
            'updated_at': firestore.SERVER_TIMESTAMP,
            'email_connected': True
        }, merge=True)

        # Get email address using the credentials
        try:
            service = get_gmail_service(credentials_dict=credentials.dict())
            profile = service.users().getProfile(userId='me').execute()
            email_address = profile.get('emailAddress', 'Unknown Email')

            # Also store in email_accounts collection for consistency
            accounts_ref = db.collection('users').document(user_data['uid']).collection('email_accounts')
            accounts_ref.document('primary').set({
                'credentials': credentials.dict(),
                'email': email_address,
                'provider': 'Gmail',
                'accountId': 'primary',
                'primary': True,
                'lastSynced': datetime.now().isoformat(),
                'created_at': firestore.SERVER_TIMESTAMP
            }, merge=True)
        except Exception as e:
            logger.error(f"Error getting email address: {str(e)}")

        return {"message": "Gmail credentials stored successfully"}
    except Exception as e:
        logger.error(f"Error storing credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error storing credentials: {str(e)}"
        )

@router.post("/refresh-account-tokens")
async def refresh_account_tokens(
    user_data: dict = Depends(verify_token)
):
    """Refresh expired tokens for all user accounts"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        user_id = user_data['uid']
        db = firestore.client()

        refresh_results = []

        # Get all accounts for the user
        accounts_ref = db.collection('users').document(user_id).collection('email_accounts')
        accounts = accounts_ref.get()

        for account_doc in accounts:
            account_id = account_doc.id
            account_data = account_doc.to_dict()

            if 'credentials' not in account_data:
                refresh_results.append({
                    'account_id': account_id,
                    'email': account_data.get('email', 'Unknown'),
                    'status': 'no_credentials',
                    'message': 'No credentials found'
                })
                continue

            try:
                # Try to create and refresh credentials
                credentials_dict = account_data['credentials']
                creds = Credentials.from_authorized_user_info(info=credentials_dict, scopes=SCOPES)

                if creds.expired and creds.refresh_token:
                    logger.info(f"Refreshing credentials for account {account_id}")
                    creds.refresh(GoogleRequest())

                    # Update credentials in Firestore
                    update_credentials_in_firestore(user_id, account_id, creds)

                    refresh_results.append({
                        'account_id': account_id,
                        'email': account_data.get('email', 'Unknown'),
                        'status': 'refreshed',
                        'message': 'Credentials refreshed successfully'
                    })
                elif creds.valid:
                    refresh_results.append({
                        'account_id': account_id,
                        'email': account_data.get('email', 'Unknown'),
                        'status': 'valid',
                        'message': 'Credentials are still valid'
                    })
                else:
                    refresh_results.append({
                        'account_id': account_id,
                        'email': account_data.get('email', 'Unknown'),
                        'status': 'invalid',
                        'message': 'Credentials are invalid and cannot be refreshed'
                    })

            except Exception as e:
                logger.error(f"Error refreshing credentials for account {account_id}: {str(e)}")
                refresh_results.append({
                    'account_id': account_id,
                    'email': account_data.get('email', 'Unknown'),
                    'status': 'error',
                    'message': f'Error refreshing: {str(e)}'
                })

        # Also check legacy primary account
        try:
            user_doc = db.collection('users').document(user_id).get()
            if user_doc.exists:
                user_data_dict = user_doc.to_dict()
                if 'gmail_credentials' in user_data_dict:
                    try:
                        credentials_dict = user_data_dict['gmail_credentials']
                        creds = Credentials.from_authorized_user_info(info=credentials_dict, scopes=SCOPES)

                        if creds.expired and creds.refresh_token:
                            logger.info(f"Refreshing legacy primary credentials")
                            creds.refresh(GoogleRequest())

                            # Update credentials in Firestore
                            update_credentials_in_firestore(user_id, "primary", creds)

                            refresh_results.append({
                                'account_id': 'primary_legacy',
                                'email': user_data_dict.get('primary_email', 'Unknown'),
                                'status': 'refreshed',
                                'message': 'Legacy primary credentials refreshed successfully'
                            })
                        elif creds.valid:
                            refresh_results.append({
                                'account_id': 'primary_legacy',
                                'email': user_data_dict.get('primary_email', 'Unknown'),
                                'status': 'valid',
                                'message': 'Legacy primary credentials are still valid'
                            })
                        else:
                            refresh_results.append({
                                'account_id': 'primary_legacy',
                                'email': user_data_dict.get('primary_email', 'Unknown'),
                                'status': 'invalid',
                                'message': 'Legacy primary credentials are invalid and cannot be refreshed'
                            })

                    except Exception as e:
                        logger.error(f"Error refreshing legacy primary credentials: {str(e)}")
                        refresh_results.append({
                            'account_id': 'primary_legacy',
                            'email': user_data_dict.get('primary_email', 'Unknown'),
                            'status': 'error',
                            'message': f'Error refreshing legacy credentials: {str(e)}'
                        })
        except Exception as e:
            logger.error(f"Error checking legacy credentials: {str(e)}")

        return {
            'message': 'Token refresh completed',
            'results': refresh_results,
            'total_accounts': len(refresh_results),
            'refreshed_count': len([r for r in refresh_results if r['status'] == 'refreshed']),
            'valid_count': len([r for r in refresh_results if r['status'] == 'valid']),
            'error_count': len([r for r in refresh_results if r['status'] in ['error', 'invalid', 'no_credentials']])
        }

    except Exception as e:
        logger.error(f"Error in refresh_account_tokens: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error refreshing tokens: {str(e)}"
        )

@router.post("/upload-credentials-file")
async def upload_credentials_file(
    file: UploadFile = File(...),
    user_data: dict = Depends(verify_token)
):
    """Upload and store Gmail API credentials from a JSON file"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Read the file content
        content = await file.read()
        credentials_dict = json.loads(content)

        # Validate the credentials format
        if not all(key in credentials_dict for key in ['token', 'refresh_token', 'token_uri', 'client_id', 'client_secret', 'scopes']):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid credentials format in the JSON file"
            )

        # Store in Firestore
        db = firestore.client()

        # Store both in legacy location and in email_accounts collection
        db.collection('users').document(user_data['uid']).set({
            'gmail_credentials': credentials_dict,
            'updated_at': firestore.SERVER_TIMESTAMP,
            'email_connected': True
        }, merge=True)

        # Get email address using the credentials
        try:
            service = get_gmail_service(credentials_dict=credentials_dict)
            profile = service.users().getProfile(userId='me').execute()
            email_address = profile.get('emailAddress', 'Unknown Email')

            # Also store in email_accounts collection for consistency
            accounts_ref = db.collection('users').document(user_data['uid']).collection('email_accounts')
            accounts_ref.document('primary').set({
                'credentials': credentials_dict,
                'email': email_address,
                'provider': 'Gmail',
                'accountId': 'primary',
                'primary': True,
                'lastSynced': datetime.now().isoformat(),
                'created_at': firestore.SERVER_TIMESTAMP
            }, merge=True)
        except Exception as e:
            logger.error(f"Error getting email address: {str(e)}")

        return {"message": "Gmail credentials from file uploaded and stored successfully"}
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON format in the uploaded file"
        )
    except Exception as e:
        logger.error(f"Error uploading credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading credentials: {str(e)}"
        )

@router.get("/credentials")
async def get_gmail_credentials(user_data: dict = Depends(verify_token)):
    """Get stored Gmail API credentials status"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        db = firestore.client()

        # Check both in legacy location and in email_accounts collection
        user_doc = db.collection('users').document(user_data['uid']).get()
        has_legacy_credentials = user_doc.exists and 'gmail_credentials' in user_doc.to_dict()

        # Check email_accounts collection
        accounts_ref = db.collection('users').document(user_data['uid']).collection('email_accounts')
        accounts = accounts_ref.get()
        has_account_credentials = len(accounts) > 0

        return {
            "has_credentials": has_legacy_credentials or has_account_credentials,
            "accounts_count": len(accounts) if has_account_credentials else 0
        }
    except Exception as e:
        logger.error(f"Error retrieving credentials: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving credentials: {str(e)}"
        )

@router.post("/search")
async def search_emails(
    search_query: EmailSearchQuery,
    user_data: dict = Depends(verify_token)
):
    """Search for emails using a query string with account-specific filtering"""
    try:
        # Log the user data for debugging
        logger.info(f"User data in search_emails: {user_data}")

        # Check if uid exists in user_data
        if 'uid' not in user_data:
            logger.error(f"Missing uid in user_data: {user_data}")
            # Try to use user_id if available
            if 'user_id' in user_data:
                logger.info(f"Using user_id instead of uid: {user_data['user_id']}")
                user_id = user_data['user_id']
            else:
                logger.error("No uid or user_id found in user_data")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials: missing user ID"
                )
        else:
            user_id = user_data['uid']

        # Apply rate limiting
        if not check_rate_limit(user_id):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        logger.info(f"Searching emails for user {user_id} with query: {search_query.query}, account: {search_query.account_id}")

        # Get credentials for the specified account
        try:
            credentials = get_credentials_for_account(user_id, search_query.account_id)
            logger.info(f"Retrieved credentials for account {search_query.account_id}")
            service = get_gmail_service(credentials_dict=credentials)
            logger.info(f"Created Gmail service for account {search_query.account_id}")
        except Exception as cred_error:
            logger.error(f"Error getting credentials or creating service: {str(cred_error)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error accessing Gmail account: {str(cred_error)}"
            )

        # Limit max_results to avoid excessive API calls
        max_results = min(search_query.max_results or 50, 100)

        message_ids = search_messages(
            service,
            search_string=search_query.query,
            max_results=max_results
        )

        # Prepare storage in Firestore
        db = firestore.client()
        user_id = user_data['uid']
        account_id = search_query.account_id

        # Track metadata for this search
        metadata_ref = db.collection('users').document(user_id).collection('metadata').document(f'email_search_{account_id}')
        metadata_ref.set({
            'last_query': search_query.query,
            'max_results': max_results,
            'results_count': len(message_ids),
            'account_id': account_id,
            'timestamp': firestore.SERVER_TIMESTAMP
        }, merge=True)

        email_previews = []
        # Process batch of message IDs
        for i in range(0, len(message_ids), 10):  # Process in batches of 10
            batch_ids = message_ids[i:i+10]

            for msg_id in batch_ids:
                try:
                    # Check if we already have this email in Firestore
                    # Create a composite ID that includes the account ID
                    composite_id = f"{account_id}_{msg_id}"

                    email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
                    email_doc = email_ref.get()

                    email_data = None
                    if email_doc.exists:
                        # Use cached email data
                        email_data = email_doc.to_dict()

                        # Update last accessed timestamp
                        email_ref.update({
                            'last_accessed': firestore.SERVER_TIMESTAMP
                        })
                    else:
                        # Fetch email details from Gmail API
                        email_data = get_message_details(service, msg_id=msg_id)

                        # Add account ID to email data
                        email_data['accountId'] = account_id

                        # Store in Firestore
                        email_storage.store_raw_email(email_data, user_id, account_id)

                    # Create a preview for the response
                    preview = {
                        'id': msg_id,
                        'subject': email_data.get('subject', 'No Subject'),
                        'from': email_data.get('from', 'Unknown'),
                        'to': email_data.get('to', ''),
                        'date': email_data.get('date', ''),
                        'has_attachments': len(email_data.get('attachments', [])) > 0,
                        'snippet': email_data.get('snippet', ''),
                        'accountId': account_id
                    }

                    email_previews.append(preview)
                except Exception as e:
                    logger.error(f"Error processing email {msg_id}: {str(e)}")
                    # Continue with next email instead of failing the whole batch

            # Add a small delay between batches to avoid rate limits
            if i + 10 < len(message_ids):
                time.sleep(0.2)  # 200ms delay

        return {"emails": email_previews}
    except Exception as e:
        logger.error(f"Error searching emails: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching emails: {str(e)}"
        )

@router.post("/search-by-date")
async def search_emails_by_date(
    date_search: EmailDateSearch,
    user_data: dict = Depends(verify_token)
):
    """Search for emails on a specific date"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Build date-based search query
        search_query = get_date_search_query(date_search.date)

        # Get credentials
        credentials = get_credentials_for_account(user_data['uid'], date_search.account_id)
        service = get_gmail_service(credentials_dict=credentials)

        # Use the search function with the date query
        message_ids = search_messages(service, search_string=search_query)

        # Track metadata for this search
        db = firestore.client()
        user_id = user_data['uid']
        account_id = date_search.account_id

        metadata_ref = db.collection('users').document(user_id).collection('metadata').document(f'email_search_{account_id}')
        metadata_ref.set({
            'last_query': search_query,
            'date_search': date_search.date,
            'results_count': len(message_ids),
            'account_id': account_id,
            'timestamp': firestore.SERVER_TIMESTAMP
        }, merge=True)

        email_previews = []
        # Process batch of message IDs
        for i in range(0, len(message_ids), 10):  # Process in batches of 10
            batch_ids = message_ids[i:i+10]

            for msg_id in batch_ids:
                try:
                    # Check if we already have this email in Firestore
                    # Create a composite ID that includes the account ID
                    composite_id = f"{account_id}_{msg_id}"

                    email_ref = db.collection('users').document(user_id).collection('emails').document(composite_id)
                    email_doc = email_ref.get()

                    email_data = None
                    if email_doc.exists:
                        # Use cached email data
                        email_data = email_doc.to_dict()

                        # Update last accessed timestamp
                        email_ref.update({
                            'last_accessed': firestore.SERVER_TIMESTAMP
                        })
                    else:
                        # Fetch email details from Gmail API
                        email_data = get_message_details(service, msg_id=msg_id)

                        # Add account ID to email data
                        email_data['accountId'] = account_id

                        # Store in Firestore
                        email_storage.store_raw_email(email_data, user_id, account_id)

                    # Create a preview for the response
                    preview = {
                        'id': msg_id,
                        'subject': email_data.get('subject', 'No Subject'),
                        'from': email_data.get('from', 'Unknown'),
                        'to': email_data.get('to', ''),
                        'date': email_data.get('date', ''),
                        'has_attachments': len(email_data.get('attachments', [])) > 0,
                        'snippet': email_data.get('snippet', ''),
                        'accountId': account_id
                    }

                    email_previews.append(preview)
                except Exception as e:
                    logger.error(f"Error processing email {msg_id}: {str(e)}")
                    # Continue with next email instead of failing the whole batch

            # Add a small delay between batches to avoid rate limits
            if i + 10 < len(message_ids):
                time.sleep(0.2)  # 200ms delay

        return {"emails": email_previews}
    except Exception as e:
        logger.error(f"Error searching emails by date: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching emails by date: {str(e)}"
        )

@router.get("/email/{email_id}")
async def get_email(
    email_id: str,
    account_id: Optional[str] = "primary",
    user_data: dict = Depends(verify_token)
):
    """Get details of a specific email with account-specific handling"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        user_id = user_data['uid']

        # First check if we have this email in Firestore
        # Create a composite ID that includes the account ID
        composite_id = f"{account_id}_{email_id}"

        # Try to get from Firestore first
        email_data = email_storage.get_email_by_id(email_id, user_id, account_id)

        if email_data:
            logger.info(f"Email {email_id} found in Firestore cache")
            return email_data

        # If not found in Firestore, fetch from Gmail API
        logger.info(f"Email {email_id} not found in Firestore, fetching from Gmail API")

        # Get credentials
        credentials = get_credentials_for_account(user_id, account_id)
        service = get_gmail_service(credentials_dict=credentials)

        # Get email details from Gmail API
        email_data = get_message_details(service, msg_id=email_id)

        # Add account ID to email data
        email_data['accountId'] = account_id

        # Store in Firestore for future reference
        email_storage.store_raw_email(email_data, user_id, account_id)

        return email_data
    except Exception as e:
        logger.error(f"Error getting email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting email: {str(e)}"
        )

@router.get("/attachment/{email_id}/{attachment_id}/{filename}")
async def get_attachment(
    email_id: str,
    attachment_id: str,
    filename: str,
    account_id: Optional[str] = "primary",
    user_data: dict = Depends(verify_token)
):
    """Download an email attachment and upload it to Firebase Storage"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        logger.info(f"Attachment request received for email_id: {email_id}, attachment_id: {attachment_id}, filename: {filename}")
        user_id = user_data['uid']

        # Check if we already have this attachment in Firestore
        db = firestore.client()
        attachments_ref = db.collection('users').document(user_id).collection('attachments')

        # Create a composite ID that includes the account ID
        composite_attachment_id = f"{account_id}_{email_id}_{attachment_id}"
        query = attachments_ref.where('composite_id', '==', composite_attachment_id).limit(1)
        existing_attachments = query.get()

        if len(existing_attachments) > 0:
            # Return the existing URL
            existing_url = existing_attachments[0].to_dict().get('url')
            if existing_url:
                logger.info(f"Found existing attachment URL: {existing_url}")
                return {"url": existing_url}

        # Get Gmail service
        credentials = get_credentials_for_account(user_id, account_id)
        service = get_gmail_service(credentials_dict=credentials)

        # Download attachment and upload to Firebase Storage
        file_url = download_attachment(service, 'me', email_id, attachment_id, filename, user_id)
        logger.info(f"Successfully downloaded and uploaded attachment: {file_url}")

        # Store attachment info in Firestore
        attachments_ref.add({
            'email_id': email_id,
            'attachment_id': attachment_id,
            'composite_id': composite_attachment_id,
            'filename': filename,
            'url': file_url,
            'user_id': user_id,
            'account_id': account_id,
            'created_at': firestore.SERVER_TIMESTAMP
        })

        return {"url": file_url}
    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Unhandled exception in get_attachment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting attachment: {str(e)}"
        )

@router.get("/attachment-data/{email_id}/{attachment_id}/{filename}")
async def get_attachment_data_only(
    email_id: str,
    attachment_id: str,
    filename: str,
    account_id: Optional[str] = "primary",
    user_data: dict = Depends(verify_token)
):
    """Get raw attachment data directly from Gmail without storing in Firebase"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        logger.info(f"Direct attachment request for email_id: {email_id}, attachment_id: {attachment_id}, filename: {filename}")
        user_id = user_data['uid']

        # Get Gmail service
        credentials = get_credentials_for_account(user_id, account_id)
        service = get_gmail_service(credentials_dict=credentials)

        # Get attachment data directly
        attachment_data = get_attachment_data_direct(service, email_id, attachment_id)

        # Return success message with size information
        return {
            "success": True,
            "filename": filename,
            "size": len(attachment_data),
            "message": f"Successfully retrieved attachment data for {filename} ({len(attachment_data)} bytes)"
        }
    except Exception as e:
        logger.error(f"Error getting attachment data directly: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting attachment data: {str(e)}"
        )

@router.get("/account-info")
async def get_account_info(
    account_id: Optional[str] = "primary",
    user_data: dict = Depends(verify_token)
):
    """Get information about the connected Gmail account"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get credentials
        credentials = get_credentials_for_account(user_data['uid'], account_id)
        service = get_gmail_service(credentials_dict=credentials)

        # Get user profile
        profile = service.users().getProfile(userId='me').execute()

        return {
            "email": profile.get('emailAddress', 'Unknown'),
            "messagesTotal": profile.get('messagesTotal', 0),
            "threadsTotal": profile.get('threadsTotal', 0),
            "historyId": profile.get('historyId', ''),
            "connected": True,
            "accountId": account_id
        }
    except Exception as e:
        logger.error(f"Error getting account info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting account info: {str(e)}"
        )

@router.get("/accounts")
async def get_all_accounts(
    user_data: dict = Depends(verify_token)
):
    """Get all connected email accounts for the user"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Firestore reference
        db = firestore.client()
        user_id = user_data['uid']
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            logger.warning(f"No user document found for UID: {user_id}")
            return {"accounts": []}

        # Store accounts by email address to avoid duplicates
        accounts_by_email = {}
        user_data_dict = user_doc.to_dict()

        logger.info(f"Retrieving accounts for user {user_id}...")

        # First, check email_accounts collection (these take precedence)
        accounts_ref = user_ref.collection('email_accounts')
        accounts_docs = accounts_ref.get()

        logger.info(f"Found {len(accounts_docs)} accounts in email_accounts collection")

        for account_doc in accounts_docs:
            account_data = account_doc.to_dict()
            logger.info(f"Processing account document ID: {account_doc.id}")

            try:
                if 'credentials' in account_data:
                    service = get_gmail_service(credentials_dict=account_data['credentials'])
                    profile = service.users().getProfile(userId='me').execute()
                    email_address = profile.get('emailAddress', account_data.get('email', 'Gmail Account'))

                    logger.info(f"Retrieved profile for account {account_doc.id}: {email_address}")

                    # Store in our email-indexed dictionary
                    accounts_by_email[email_address] = {
                        "id": account_doc.id,
                        "email": email_address,
                        "provider": account_data.get('provider', 'Gmail'),
                        "accountId": account_data.get('accountId', account_doc.id),
                        "primary": account_data.get('primary', False),
                        "lastSynced": account_data.get('lastSynced', datetime.now().isoformat()),
                        "connected": True
                    }
                    logger.info(f"Added account to response: {email_address} (ID: {account_doc.id})")
            except Exception as e:
                logger.error(f"Error getting profile for account {account_doc.id}: {str(e)}")
                email_address = account_data.get('email', 'Gmail Account')

                # Only add if we don't already have this email
                if email_address not in accounts_by_email:
                    accounts_by_email[email_address] = {
                        "id": account_doc.id,
                        "email": email_address,
                        "provider": account_data.get('provider', 'Gmail'),
                        "accountId": account_data.get('accountId', account_doc.id),
                        "primary": account_data.get('primary', False),
                        "lastSynced": account_data.get('lastSynced', datetime.now().isoformat()),
                        "connected": True
                    }
                    logger.info(f"Added account (using stored email) to response: {email_address} (ID: {account_doc.id})")

        # Then check for legacy single account (only add if not already in the dictionary)
        if 'gmail_credentials' in user_data_dict:
            logger.info("Found legacy gmail_credentials in user document")
            try:
                service = get_gmail_service(credentials_dict=user_data_dict['gmail_credentials'])
                profile = service.users().getProfile(userId='me').execute()
                email_address = profile.get('emailAddress', 'Gmail Account')

                logger.info(f"Retrieved profile for legacy account: {email_address}")

                # Only add if not already in our accounts dictionary
                if email_address not in accounts_by_email:
                    accounts_by_email[email_address] = {
                        "id": "primary",
                        "email": email_address,
                        "provider": "Gmail",
                        "accountId": "primary",
                        "primary": True,
                        "lastSynced": datetime.now().isoformat(),
                        "connected": True
                    }
                    logger.info(f"Added legacy account to response: {email_address}")
                else:
                    logger.info(f"Skipping legacy account (already in collection): {email_address}")
            except Exception as e:
                logger.error(f"Error getting profile for legacy account: {str(e)}")
                # Only add if we don't have any accounts yet
                if 'Gmail Account' not in accounts_by_email and len(accounts_by_email) == 0:
                    accounts_by_email['Gmail Account'] = {
                        "id": "primary",
                        "email": "Gmail Account",
                        "provider": "Gmail",
                        "accountId": "primary",
                        "primary": True,
                        "lastSynced": datetime.now().isoformat(),
                        "connected": True
                    }
                    logger.info(f"Added generic Gmail Account (legacy) as fallback")

        # Convert the dictionary to a list for the response
        accounts = list(accounts_by_email.values())

        logger.info(f"Returning {len(accounts)} unique email accounts for user {user_id}")
        for i, account in enumerate(accounts):
            logger.info(f"  Account {i+1}: {account['email']} (ID: {account['id']}, accountId: {account['accountId']})")

        return {"accounts": accounts}
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting accounts: {str(e)}"
        )

@router.post("/disconnect")
async def disconnect_account(
    request: AccountRequest,
    user_data: dict = Depends(verify_token)
):
    """Disconnect a specific Gmail account or the primary account if no accountId is provided"""
    try:
        # Apply rate limiting
        if not check_rate_limit(user_data['uid']):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded. Please try again later."
            )

        # Get Firestore reference
        db = firestore.client()
        user_id = user_data['uid']
        user_ref = db.collection('users').document(user_id)

        if request.accountId and request.accountId != "primary":
            # Delete specific account from the accounts collection
            account_ref = user_ref.collection('email_accounts').document(request.accountId)
            account_doc = account_ref.get()

            if not account_doc.exists:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Account with ID {request.accountId} not found"
                )

            # Perform cleanup in transaction to ensure consistency
            transaction = db.transaction()

            @firestore.transactional
            def disconnect_in_transaction(transaction, account_ref, account_id, user_id):
                # First get account email
                account_doc = account_ref.get(transaction=transaction)
                account_data = account_doc.to_dict()
                account_email = account_data.get('email', 'Unknown')

                # Delete the account
                transaction.delete(account_ref)

                # Delete any scheduled jobs for this account
                email_scheduler_jobs_ref = db.collection('email_scheduler_jobs')
                job_query = email_scheduler_jobs_ref.where('user_id', '==', user_id).where('account_id', '==', account_id).get()

                for job_doc in job_query:
                    email_scheduler_jobs_ref.document(job_doc.id).update({
                        'status': 'cancelled',
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })

                # Delete any email configurations for this account
                configs_ref = db.collection('users').document(user_id).collection('email_configs')
                config_query = configs_ref.where('account_id', '==', account_id).get()

                for config_doc in config_query:
                    logger.info(f"Deleting email config {config_doc.id} for account {account_id}")
                    configs_ref.document(config_doc.id).delete()

                # Return the email for logging
                return account_email

            # Execute the transaction
            account_email = disconnect_in_transaction(transaction, account_ref, request.accountId, user_id)
            logger.info(f"Disconnected account {request.accountId} ({account_email})")

            return {"success": True, "message": f"Account {request.accountId} disconnected successfully"}
        else:
            # Update the document to remove the primary Gmail credentials
            # First get email address for logging
            primary_email = "Unknown"
            try:
                user_doc = user_ref.get()
                if user_doc.exists:
                    user_data = user_doc.to_dict()
                    primary_email = user_data.get('primary_email', 'Unknown')
            except Exception as e:
                logger.error(f"Error getting primary email: {str(e)}")

            # Perform cleanup in transaction to ensure consistency
            transaction = db.transaction()

            @firestore.transactional
            def disconnect_primary_in_transaction(transaction, user_ref, user_id):
                # Update user document
                transaction.update(user_ref, {
                    'gmail_credentials': firestore.DELETE_FIELD,
                    'primary_email': firestore.DELETE_FIELD,
                    'email_connected': False,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })

                # Remove primary account from email_accounts collection
                primary_account_ref = user_ref.collection('email_accounts').document('primary')
                primary_doc = primary_account_ref.get(transaction=transaction)

                if primary_doc.exists:
                    transaction.delete(primary_account_ref)

                # Delete any scheduled jobs for this account
                email_scheduler_jobs_ref = db.collection('email_scheduler_jobs')
                job_query = email_scheduler_jobs_ref.where('user_id', '==', user_id).where('account_id', '==', 'primary').get()

                for job_doc in job_query:
                    email_scheduler_jobs_ref.document(job_doc.id).update({
                        'status': 'cancelled',
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })

                # Delete any email configurations for the primary account
                configs_ref = db.collection('users').document(user_id).collection('email_configs')
                config_query = configs_ref.where('account_id', '==', 'primary').get()

                for config_doc in config_query:
                    logger.info(f"Deleting email config {config_doc.id} for primary account")
                    configs_ref.document(config_doc.id).delete()

            # Execute the transaction
            disconnect_primary_in_transaction(transaction, user_ref, user_id)
            logger.info(f"Disconnected primary Gmail account ({primary_email})")

            return {"success": True, "message": "Primary Gmail account disconnected successfully"}
    except Exception as e:
        logger.error(f"Error disconnecting account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error disconnecting account: {str(e)}"
        )