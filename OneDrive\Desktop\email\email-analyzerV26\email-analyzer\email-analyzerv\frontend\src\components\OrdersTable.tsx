import React, { useState } from 'react';
import {
  Box,
  Button,
  Chip,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Tooltip,
  Typography
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import JsonIcon from '@mui/icons-material/Code';
import ExcelIcon from '@mui/icons-material/GridOn';
import { EmailAnalysisData } from '../services/firestore';
import { format } from 'date-fns';
import { exportToJson, exportToCsv, prepareEmailAnalysisForExport } from '../utils/exportUtils';

// Define the type for structured analysis data
interface StructuredAnalysisData {
  summary?: string;
  key_dates?: string[];
  action_items?: string[];
  sentiment?: string;
  category?: string;
  is_order?: boolean;
  is_invoice?: boolean;
  order_number?: string;
  total_amount?: string;
  status?: string;
  vendor?: { name?: string; [key: string]: any };
  customer?: { name?: string; [key: string]: any };
  document_info?: {
    order_number?: string;
    po_number?: string;
    status?: string;
    type?: string;
    [key: string]: any;
  };
  parties?: {
    vendor?: { name?: string; [key: string]: any };
    customer?: { name?: string; [key: string]: any };
    [key: string]: any;
  };
  financial_details?: {
    total_amount?: string;
    [key: string]: any;
  };
  items?: Array<any>;
  email_metadata?: {
    subject?: string;
    from?: string;
    to?: string;
    timestamp?: string;
    [key: string]: any;
  };
  analyzed_at?: string;
  [key: string]: any;
}

// Define the type for order data
interface OrderData {
  id: string;
  accountId: string;
  emailId: string;
  date: string;
  from: string;
  subject: string;
  orderNumber: string;
  vendor: string;
  customer: string;
  totalAmount: string;
  status: string;
  items: number;
  analysis: StructuredAnalysisData;
  timestamp?: string;
  actions?: React.ReactNode;
}

// Define props for the component
interface OrdersTableProps {
  orders: EmailAnalysisData[];
  onViewOrder: (order: EmailAnalysisData) => void;
}

// Helper function to extract order data from email analysis
const extractOrderData = (analysis: EmailAnalysisData): OrderData => {
  const analysisData = analysis.analysis as StructuredAnalysisData || {};

  // Extract order number from various possible locations (prioritize direct fields)
  const orderNumber =
    analysis.orderNumber ||
    analysis.order_number ||
    analysisData.document_info?.order_number ||
    analysisData.document_info?.po_number ||
    analysisData.order_number ||
    'N/A';

  // Extract vendor information (prioritize direct fields)
  const vendor =
    analysis.vendor ||
    analysis.vendor_name ||
    analysisData.parties?.vendor?.name ||
    analysisData.vendor?.name ||
    'N/A';

  // Extract customer information (prioritize direct fields)
  const customer =
    analysis.customer ||
    analysis.customer_name ||
    analysisData.parties?.customer?.name ||
    analysisData.customer?.name ||
    'N/A';

  // Extract total amount (prioritize direct fields)
  const totalAmount =
    analysis.totalAmount ||
    analysis.total_amount ||
    analysisData.financial_details?.total_amount ||
    analysisData.total_amount ||
    'N/A';

  // Extract status (prioritize direct fields)
  const status =
    analysis.status ||
    analysisData.document_info?.status ||
    analysisData.status ||
    'pending';

  // Extract items count (prioritize direct fields)
  const items =
    analysis.items ||
    (Array.isArray(analysisData.items) ? analysisData.items.length : 0) ||
    0;

  // Format date
  let dateStr = 'Unknown';
  try {
    // Try multiple possible date fields in order of preference
    const possibleDates = [
      analysis.date,
      analysis.analyzedAt,
      analysisData.analyzed_at,
      analysisData.dates?.order_date,
      analysisData.dates?.invoice_date,
      analysisData.dates?.document_date,
      analysisData.email_metadata?.timestamp,
      (analysis as any).timestamp
    ];

    // Find the first valid date
    for (const dateValue of possibleDates) {
      if (dateValue) {
        const date = new Date(dateValue.toString());
        if (!isNaN(date.getTime())) {
          dateStr = format(date, 'MMM dd, yyyy HH:mm');
          break;
        }
      }
    }

    // If we still don't have a date, try to extract it from the email subject or body
    if (dateStr === 'Unknown' && analysisData.email_metadata?.subject) {
      const dateMatch = analysisData.email_metadata.subject.match(/\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/);
      if (dateMatch) {
        const extractedDate = new Date(dateMatch[0]);
        if (!isNaN(extractedDate.getTime())) {
          dateStr = format(extractedDate, 'MMM dd, yyyy');
        }
      }
    }
  } catch (e) {
    console.error('Error formatting date:', e);
  }

  // Create view action button
  const actions = (
    <Tooltip title="View Order Details">
      <IconButton size="small">
        <VisibilityIcon fontSize="small" />
      </IconButton>
    </Tooltip>
  );

  return {
    id: analysis.id || '',
    accountId: analysis.accountId || '',
    emailId: analysis.emailId || '',
    date: dateStr,
    from: analysisData.email_metadata?.from || analysis.from || 'Unknown',
    subject: analysisData.email_metadata?.subject || analysis.subject || 'No Subject',
    orderNumber,
    vendor,
    customer,
    totalAmount,
    status,
    items,
    analysis: analysisData,
    actions
  };
};

// Define the type for column data
interface Column {
  id: keyof OrderData;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
}

// Define the columns for the table
const columns: Column[] = [
  { id: 'date', label: 'Date', minWidth: 120 },
  { id: 'orderNumber', label: 'Order #', minWidth: 120 },
  { id: 'subject', label: 'Subject', minWidth: 200 },
  { id: 'from', label: 'From', minWidth: 150 },
  { id: 'vendor', label: 'Vendor', minWidth: 150 },
  { id: 'customer', label: 'Customer', minWidth: 150 },
  { id: 'totalAmount', label: 'Total', minWidth: 100, align: 'right' },
  { id: 'items', label: 'Items', minWidth: 80, align: 'center' },
  { id: 'status', label: 'Status', minWidth: 120 },
  { id: 'actions', label: '', minWidth: 50, align: 'center' }
];

/**
 * Component for displaying orders in a table format
 */
const OrdersTable: React.FC<OrdersTableProps> = ({ orders, onViewOrder }) => {
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for sorting
  const [orderBy, setOrderBy] = useState<keyof OrderData>('date');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');

  // Extract and transform order data
  const orderData: OrderData[] = orders.map(extractOrderData);

  // Handle sort request
  const handleRequestSort = (property: keyof OrderData) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Sort function
  const sortedData = React.useMemo(() => {
    return [...orderData].sort((a, b) => {
      const aValue = a[orderBy] || '';
      const bValue = b[orderBy] || '';

      if (order === 'asc') {
        if (aValue < bValue) return -1;
        if (aValue > bValue) return 1;
        return 0;
      } else {
        if (aValue > bValue) return -1;
        if (aValue < bValue) return 1;
        return 0;
      }
    });
  }, [orderData, order, orderBy]);

  // Handle page change
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  // Handle export to JSON
  const handleExportJson = () => {
    const exportData = orders.map(order => prepareEmailAnalysisForExport(order as any));
    exportToJson(exportData, `orders-export-${new Date().toISOString().slice(0, 10)}`);
  };

  // Handle export to Excel/CSV
  const handleExportExcel = () => {
    const exportData = orders.map(order => prepareEmailAnalysisForExport(order as any));
    exportToCsv(exportData, `orders-export-${new Date().toISOString().slice(0, 10)}`);
  };

  // Render status chip
  const renderStatusChip = (status: string) => {
    let color: 'success' | 'warning' | 'error' | 'default' | 'primary' | 'secondary' | 'info' = 'default';

    const statusLower = status.toLowerCase();
    if (statusLower.includes('complete') || statusLower.includes('paid') || statusLower.includes('shipped')) {
      color = 'success';
    } else if (statusLower.includes('pending') || statusLower.includes('processing')) {
      color = 'warning';
    } else if (statusLower.includes('cancel') || statusLower.includes('error')) {
      color = 'error';
    } else if (statusLower.includes('draft')) {
      color = 'info';
    }

    return <Chip label={status} color={color} size="small" />;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" component="h2">
          Orders ({orders.length})
        </Typography>
        <Box>
          <Button
            startIcon={<JsonIcon />}
            onClick={handleExportJson}
            sx={{ mr: 1 }}
            variant="outlined"
            size="small"
          >
            Export JSON
          </Button>
          <Button
            startIcon={<ExcelIcon />}
            onClick={handleExportExcel}
            variant="outlined"
            size="small"
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 'calc(100vh - 250px)' }}>
          <Table stickyHeader aria-label="orders table" size="small">
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{ minWidth: column.minWidth }}
                    sortDirection={orderBy === column.id ? order : false}
                  >
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? order : 'asc'}
                      onClick={() => handleRequestSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row) => {
                  return (
                    <TableRow
                      hover
                      role="checkbox"
                      tabIndex={-1}
                      key={row.id}
                      onClick={() => {
                        const originalOrder = orders.find(o => o.id === row.id);
                        if (originalOrder) {
                          onViewOrder(originalOrder);
                        }
                      }}
                      sx={{ cursor: 'pointer' }}
                    >
                      {columns.map((column) => {
                        const value = row[column.id];

                        if (column.id === 'actions') {
                          return (
                            <TableCell key={column.id} align={column.align} onClick={(e) => e.stopPropagation()}>
                              <Tooltip title="View Order Details">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    const originalOrder = orders.find(o => o.id === row.id);
                                    if (originalOrder) {
                                      onViewOrder(originalOrder);
                                    }
                                  }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          );
                        }

                        return (
                          <TableCell key={column.id} align={column.align}>
                            {column.id === 'status' && typeof value === 'string'
                              ? renderStatusChip(value)
                              : column.format
                                ? column.format(value)
                                : typeof value === 'object'
                                  ? JSON.stringify(value)
                                  : String(value)}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={sortedData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default OrdersTable;
