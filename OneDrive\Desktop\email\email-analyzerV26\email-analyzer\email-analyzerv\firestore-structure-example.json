{"created_at": "2024-01-15T10:30:00Z", "email_id": "18c5f2a1b2d3e4f5", "emailId": "18c5f2a1b2d3e4f5", "composite_id": "primary_18c5f2a1b2d3e4f5", "account_id": "primary", "accountId": "primary", "user_id": "user123", "subject": "Order Confirmation #ORD-2024-001", "from": "<EMAIL>", "to": "<EMAIL>", "cc": "", "bcc": "", "date": "2024-01-15T10:30:00Z", "has_attachments": true, "email_fingerprint": "abc123def456", "category": "order_confirmation", "summary": "Order confirmation for 5 items totaling $299.99 with shipping to customer address", "sentiment": "positive", "total_amount": "$299.99", "totalAmount": "$299.99", "order_number": "ORD-2024-001", "orderNumber": "ORD-2024-001", "document_number": "ORD-2024-001", "po_number": "", "invoice_number": "", "vendor": "Example Store Inc.", "vendor_name": "Example Store Inc.", "customer": "<PERSON>", "customer_name": "<PERSON>", "is_order": true, "isOrder": true, "is_invoice": false, "isInvoice": false, "status": "confirmed", "items": 5, "key_dates": ["2024-01-15", "2024-01-20"], "keyDates": ["2024-01-15", "2024-01-20"], "action_items": ["Track shipment", "Prepare for delivery"], "actionItems": ["Track shipment", "Prepare for delivery"], "analyzed_at": "2024-01-15T10:35:00Z", "analyzedAt": "2024-01-15T10:35:00Z", "last_updated": "2024-01-15T10:35:00Z", "analysis": {"category": "order_confirmation", "document_info": {"document_type": "order_confirmation", "document_number": "ORD-2024-001", "order_number": "ORD-2024-001", "status": "confirmed"}, "parties": {"vendor": {"name": "Example Store Inc.", "address": "123 Business St, City, State 12345", "phone": "******-0123", "email": "<EMAIL>"}, "customer": {"name": "<PERSON>", "address": "456 Customer Ave, City, State 67890", "phone": "******-0456", "email": "<EMAIL>"}}, "dates": {"document_date": "2024-01-15", "ship_date": "2024-01-18", "delivery_date": "2024-01-20"}, "financial_details": {"currency": "USD", "subtotal": "$249.99", "tax_total": "$25.00", "shipping_cost": "$25.00", "total_amount": "$299.99"}, "items": [{"item_number": "SKU001", "description": "Product A", "quantity": "2", "unit_price": "$49.99", "line_total": "$99.98"}, {"item_number": "SKU002", "description": "Product B", "quantity": "3", "unit_price": "$50.00", "line_total": "$150.00"}], "analysis_results": {"summary": "Order confirmation for 5 items totaling $299.99 with shipping to customer address", "sentiment": "positive", "confidence_score": 0.95, "key_dates_extracted": 3, "action_items": ["Track shipment", "Prepare for delivery"]}}, "attachments": [{"filename": "order_receipt.pdf", "content_type": "application/pdf", "size": 245760, "url": "https://firebasestorage.googleapis.com/...", "viewer_url": "https://docs.google.com/viewer?url=...", "analysis": {"document_type": "receipt", "extracted_data_confidence": 0.98, "content_summary": "Order receipt with itemized list"}}]}