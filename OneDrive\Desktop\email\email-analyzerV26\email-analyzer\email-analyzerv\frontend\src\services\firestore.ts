import { db } from '../firebase';
import { collection, addDoc, getDoc, getDocs, query, where, doc, setDoc, updateDoc, Timestamp, limit, orderBy, onSnapshot } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Interface for email account data to be stored in Firestore
export interface EmailAccountData {
  id: string;
  provider: string;
  email: string;
  connected: boolean;
  lastSynced: string;
  accountId: string;
  primary: boolean;
  userId: string;
}

// Interface for email data to be stored in Firestore
export interface EmailData {
  id: string; // Gmail message ID
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  body: {
    html?: string;
    text?: string;
  };
  attachments?: {
    id: string;
    filename: string;
    size: number;
    contentType: string;
  }[];
  labels?: string[];
  accountId?: string;
  lastFetched: number; // Timestamp
  userId: string;
}

// Interface for email analysis data to be stored in Firestore
export interface EmailAnalysisData {
  id?: string;  // Document ID in Firestore
  emailId: string;
  email_id?: string;  // Backend version
  accountId?: string;  // Account ID the email belongs to
  account_id?: string;  // Backend version
  accountEmail?: string; // Email address associated with the account
  compositeId?: string;  // Composite ID combining account ID and email ID
  composite_id?: string;  // Backend version
  subject?: string;
  from?: string;
  to?: string;
  date?: string;
  dates?: string[] | Date[];  // Array of dates mentioned in the email
  content?: string;
  summary?: string;
  orderNumber?: string;
  order_number?: string;  // Backend version of orderNumber
  vendor?: string;
  vendor_name?: string;  // Backend version
  customer?: string;
  customer_name?: string;  // Backend version
  totalAmount?: string;
  total_amount?: string;  // Backend version of totalAmount
  status?: string;  // Order/invoice status
  items?: number;  // Number of items

  // Support both frontend and backend naming conventions
  keyDates?: string[];
  key_dates?: string[];

  actionItems?: string[];
  action_items?: string[];

  sentiment?: string;

  // Email metadata can be at top level or nested
  emailMetadata?: {
    subject: string;
    from: string;
    to: string;
    timestamp: string;
    hasAttachments?: boolean;
    attachmentCount?: number;
    attachmentNames?: string[];
  };

  email_metadata?: {
    subject: string;
    from: string;
    to: string;
    timestamp: string;
    has_attachments?: boolean;
    attachment_count?: number;
    attachment_names?: string[];
  };

  category?: string; // For categorizing emails (e.g., 'order', 'invoice', 'inquiry', etc.)

  // For order emails - using both naming conventions but not duplicating properties
  isOrder?: boolean;
  is_order?: boolean;

  orderDetails?: {
    products: string[];
    quantities: string[];
    pricing: string[];
    delivery: string;
  };

  // For invoice emails - using both naming conventions but not duplicating properties
  isInvoice?: boolean;
  is_invoice?: boolean;

  invoiceDetails?: {
    invoiceNumber: string;
    amount: string;
    dueDate: string;
    due_date: string;
    items: string[];
  };

  // Key details extracted from the email
  keyDetails?: Record<string, any>; // For storing key-value pairs of important details
  key_details?: Record<string, any>; // Backend version of keyDetails

  // Analysis metadata
  analyzedAt?: number | string; // Timestamp when the email was analyzed
  analyzed_at?: number | string; // Backend version of analyzedAt
  userId?: string; // ID of the user who owns this analysis

  // Nested analysis data from backend (updated to match Gemini output)
  analysis?: {
    category?: string;
    document_info?: {
      document_type?: string;
      document_number?: string;
      order_number?: string;
      po_number?: string;
      invoice_number?: string;
      status?: string;
    };
    parties?: {
      vendor?: {
        name?: string;
        address?: string;
        phone?: string;
        email?: string;
        tax_id?: string;
      };
      customer?: {
        name?: string;
        address?: string;
        phone?: string;
        email?: string;
        customer_id?: string;
      };
      ship_to?: {
        name?: string;
        address?: string;
        phone?: string;
      };
    };
    dates?: {
      document_date?: string;
      due_date?: string;
      ship_date?: string;
      delivery_date?: string;
      payment_date?: string;
    };
    financial_details?: {
      currency?: string;
      subtotal?: string;
      tax_total?: string;
      shipping_cost?: string;
      total_amount?: string;
      amount_paid?: string;
      balance_due?: string;
      payment_terms?: string;
    };
    items?: Array<{
      item_number?: string;
      description?: string;
      quantity?: number | string;
      unit?: string;
      unit_price?: string;
      line_total?: string;
      tax_rate?: string;
      attributes?: {
        color?: string;
        size?: string;
        material?: string;
        specifications?: string;
      };
    }>;
    analysis_results?: {
      summary?: string;
      sentiment?: string;
      confidence_score?: number;
      key_dates_extracted?: number;
      action_items?: string[];
    };
    attachments?: Array<{
      filename?: string;
      content_type?: string;
      size?: number;
      url?: string;
      viewer_url?: string;
      analysis?: {
        document_type?: string;
        extracted_data_confidence?: number;
        content_summary?: string;
      };
    }>;
    // Legacy fields for backward compatibility
    summary?: string;
    key_dates?: string[];
    action_items?: string[];
    sentiment?: string;
    is_order?: boolean;
    is_invoice?: boolean;
    order_details?: {
      products: string[];
      quantities: string[];
      pricing: string[];
      delivery: string;
    };
    invoice_details?: {
      invoice_number: string;
      amount: string;
      due_date: string;
      items: string[];
    };
    email_metadata?: {
      subject: string;
      from: string;
      to: string;
      timestamp: string;
      has_attachments?: boolean;
      attachment_count?: number;
      attachment_names?: string[];
    };
    analyzed_at?: number | string;
    email_analysis?: {
      summary?: string;
      is_order?: boolean;
      is_invoice?: boolean;
      category?: string;
      sentiment?: string;
      key_dates?: string[];
      action_items?: string[];
      attachment_urls?: Array<{
        filename: string;
        url: string;
        content_type: string;
      }>;
    };
  };

  // Direct email_analysis data from backend
  email_analysis?: {
    summary?: string;
    is_order?: boolean;
    is_invoice?: boolean;
    category?: string;
    sentiment?: string;
    key_dates?: string[];
    action_items?: string[];
    attachment_urls?: Array<{
      filename: string;
      url: string;
      content_type: string;
    }>;
  };
}

/**
 * Creates a composite document ID combining accountId and emailId
 */
function createCompositeId(emailId: string, accountId: string = 'primary'): string {
  return `${accountId}_${emailId}`;
}

// Save email data to Firestore with account ID
export const saveEmail = async (emailData: EmailData) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Add user ID to the email data
    const emailWithUserId = {
      ...emailData,
      userId: user.uid,
      lastFetched: Date.now() // Always update the lastFetched timestamp
    };

    // Reference to the emails collection for this user
    const emailsRef = collection(db, 'users', user.uid, 'emails');

    // Create composite ID for consistent storage
    const accountId = emailData.accountId || 'primary';
    const compositeId = createCompositeId(emailData.id, accountId);

    // Check if email already exists using document reference
    const emailDocRef = doc(emailsRef, compositeId);
    const emailDocSnapshot = await getDocWithRetry(emailDocRef);

    if (!emailDocSnapshot.exists()) {
      // Email doesn't exist, add it as a new document with composite ID
      await setDoc(emailDocRef, emailWithUserId);
      console.log(`Email saved with composite ID: ${compositeId}`);
      return { firestoreId: compositeId, ...emailWithUserId };
    } else {
      // Email exists, update it
      await updateDoc(emailDocRef, {
        ...emailWithUserId,
        lastFetched: Date.now()
      });
      console.log(`Email updated with composite ID: ${compositeId}`);
      return { firestoreId: compositeId, ...emailWithUserId };
    }
  } catch (error) {
    console.error('Error saving email:', error);
    throw error;
  }
};

// Helper function to retry getting a document in case of network issues
const getDocWithRetry = async (docRef: any, maxRetries = 3): Promise<any> => {
  let retries = 0;
  let lastError = null;

  while (retries < maxRetries) {
    try {
      return await getDoc(docRef);
    } catch (err) {
      lastError = err;
      retries++;
      console.warn(`Retry ${retries}/${maxRetries} for getDoc failed:`, err);
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries - 1)));
    }
  }

  // If we've exhausted retries, throw the last error
  throw lastError;
};

// Save email analysis result to Firestore with account information
export const saveEmailAnalysis = async (analysisData: EmailAnalysisData) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('No authenticated user found when trying to save email analysis');
      throw new Error('User not authenticated');
    }

    const emailId = analysisData.emailId;
    if (!emailId) {
      throw new Error('Email ID is required');
    }

    // Get account ID from data or use default
    const accountId = analysisData.accountId || 'primary';

    // Create composite ID for consistent storage
    const compositeId = createCompositeId(emailId, accountId);

    console.log(`Saving email analysis for email ID: ${emailId}, composite ID: ${compositeId}, user: ${user.uid}`);

    // Use the correct path: /users/{firebase_user_id}/email_analyses/{composite_id}
    const emailAnalysisDocRef = doc(db, 'users', user.uid, 'email_analyses', compositeId);

    // Check if the document already exists
    const docSnapshot = await getDocWithRetry(emailAnalysisDocRef);

    const now = new Date().toISOString();

    // Prepare the data with proper field mappings for both frontend and backend compatibility
    const dataToSave = {
      // Make sure we have both frontend and backend versions of key fields
      email_id: emailId,
      composite_id: compositeId,
      account_id: accountId,

      // Copy other fields from analysisData
      ...analysisData,

      // Ensure we have both naming conventions for all key fields
      is_order: analysisData.isOrder || analysisData.is_order || false,
      isOrder: analysisData.isOrder || analysisData.is_order || false,
      is_invoice: analysisData.isInvoice || analysisData.is_invoice || false,
      isInvoice: analysisData.isInvoice || analysisData.is_invoice || false,
      order_number: analysisData.orderNumber || (analysisData as any).order_number || '',
      orderNumber: analysisData.orderNumber || (analysisData as any).order_number || '',
      total_amount: analysisData.totalAmount || (analysisData as any).total_amount || '',
      totalAmount: analysisData.totalAmount || (analysisData as any).total_amount || '',

      // Key dates in both formats
      key_dates: analysisData.keyDates || analysisData.key_dates || [],
      keyDates: analysisData.keyDates || analysisData.key_dates || [],

      // Action items in both formats
      action_items: analysisData.actionItems || analysisData.action_items || [],
      actionItems: analysisData.actionItems || analysisData.action_items || [],

      // Add user ID
      user_id: user.uid,
      userId: user.uid,

      // Add timestamps
      analyzed_at: analysisData.analyzedAt || analysisData.analyzed_at || now,
      analyzedAt: analysisData.analyzedAt || analysisData.analyzed_at || now,

      // Store the full analysis result if provided
      ...(analysisData.analysis ? { analysis: analysisData.analysis } : {}),
    };

    if (!docSnapshot.exists()) {
      // Create new document
      console.log('Creating new email analysis document in Firestore');

      await setDoc(emailAnalysisDocRef, {
        ...dataToSave,
        created_at: now,
        updated_at: now
      });

      console.log(`Successfully created new document with composite ID: ${compositeId}`);
    } else {
      // Update existing document
      console.log(`Updating existing email analysis document with composite ID: ${compositeId}`);

      await updateDoc(emailAnalysisDocRef, {
        ...dataToSave,
        updated_at: now
      });

      console.log(`Successfully updated document with composite ID: ${compositeId}`);
    }

    // Also update the raw email document to indicate it has been analyzed
    const emailRef = doc(db, 'users', user.uid, 'emails', compositeId);
    try {
      await updateDoc(emailRef, {
        has_analysis: true,
        hasAnalysis: true,
        analysis_timestamp: now,
        analysisTimestamp: now
      });
    } catch (err) {
      console.warn(`Could not update email document status, may not exist: ${err}`);
    }

    return emailAnalysisDocRef;
  } catch (error) {
    console.error('Error saving email analysis:', error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw error;
  }
};

// Get all email analyses for the current user with improved retrieval and account filtering
export const getEmailAnalyses = async (accountId?: string) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('No authenticated user found when trying to get email analyses');
      throw new Error('User not authenticated');
    }

    console.log(`Fetching all email analyses for user: ${user.uid}${accountId ? `, account: ${accountId}` : ''}`);

    // Use the correct path: /users/{firebase_user_id}/email_analyses/
    const emailAnalysesRef = collection(db, 'users', user.uid, 'email_analyses');

    // Create query based on parameters
    let analysesQuery;

    if (accountId) {
      // Filter by accountId if provided
      analysesQuery = query(
        emailAnalysesRef,
        where('accountId', '==', accountId),
        orderBy('analyzed_at', 'desc')
      );
    } else {
      // Get all analyses if no accountId filter
      analysesQuery = query(
        emailAnalysesRef,
        orderBy('analyzed_at', 'desc')
      );
    }

    // Get all documents from this collection
    const querySnapshot = await getDocs(analysesQuery);
    console.log(`Found ${querySnapshot.docs.length} email analyses in Firestore`);

    if (querySnapshot.docs.length === 0) {
      console.warn('No email analyses found in Firestore for this user');
      return [];
    }

    // Process all results and include ALL fields from Firestore
    const results = querySnapshot.docs.map(doc => {
      const data = doc.data();
      const analysisData = data.analysis || data;

      return {
        id: doc.id,
        emailId: data.emailId || data.email_id || doc.id,
        accountId: data.accountId || data.account_id || 'primary',
        subject: data.subject || analysisData.subject || '',
        from: data.from || analysisData.from || '',
        to: data.to || analysisData.to || '',
        date: data.date || analysisData.date || '',
        summary: data.summary || (analysisData.summary) || '',
        category: data.category || analysisData.category || 'other',
        sentiment: data.sentiment || analysisData.sentiment || 'neutral',
        keyDetails: data.keyDetails || data.key_details || analysisData.keyDetails || analysisData.key_details || {},
        keyDates: data.keyDates || data.key_dates || analysisData.keyDates || analysisData.key_dates || [],
        actionItems: data.actionItems || data.action_items || analysisData.actionItems || analysisData.action_items || [],
        isOrder: data.isOrder || data.is_order || analysisData.isOrder || analysisData.is_order || false,
        isInvoice: data.isInvoice || data.is_invoice || analysisData.isInvoice || analysisData.is_invoice || false,
        orderNumber: data.orderNumber || data.order_number || analysisData.orderNumber || analysisData.order_number || '',
        totalAmount: data.totalAmount || data.total_amount || analysisData.totalAmount || analysisData.total_amount || '',
        analyzedAt: data.analyzedAt || data.analyzed_at || analysisData.analyzedAt || analysisData.analyzed_at || new Date().toISOString(),
        // Include the full analysis data
        analysis: analysisData
      } as unknown as EmailAnalysisData;
    });

    console.log(`Successfully retrieved ${results.length} email analyses`);

    // Log the first few analyses to help with debugging
    if (results.length > 0) {
      console.log('First analysis excerpt:', JSON.stringify(results[0]).substring(0, 200) + '...');
    }

    return results;
  } catch (error) {
    console.error('Error getting email analyses:', error);
    throw error;
  }
};

// Get all emails for the current user with account filtering
export const getEmails = async (maxResults = 50, accountId?: string) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Use the correct path: /users/{firebase_user_id}/emails/
    const emailsRef = collection(db, 'users', user.uid, 'emails');

    // Create query based on parameters
    let emailsQuery;

    if (accountId) {
      // Filter by accountId if provided
      emailsQuery = query(
        emailsRef,
        where('accountId', '==', accountId),
        limit(maxResults)
      );
      console.log(`Querying emails with accountId: ${accountId}, limit: ${maxResults}`);
    } else {
      // Get all emails if no accountId filter
      emailsQuery = query(
        emailsRef,
        limit(maxResults)
      );
      console.log(`Querying all emails, limit: ${maxResults}`);
    }

    const querySnapshot = await getDocs(emailsQuery);
    console.log(`Found ${querySnapshot.docs.length} emails in Firestore`);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data
      };
    });
  } catch (error) {
    console.error('Error getting emails:', error);
    throw error;
  }
};

// Get all email analyses without filtering for account-specific checking
export const getAllEmailAnalyses = async () => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Use the correct path: /users/{firebase_user_id}/email_analyses/
    const emailAnalysesRef = collection(db, 'users', user.uid, 'email_analyses');

    // Get all documents (limit to 500 for performance)
    const querySnapshot = await getDocs(query(emailAnalysesRef, limit(500)));
    console.log(`Found ${querySnapshot.docs.length} email analyses in getAllEmailAnalyses`);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      // Extract account_id and email_id from composite_id if present
      const compositeId = doc.id;
      let emailId = data.email_id || data.emailId || '';
      let accountId = data.account_id || data.accountId || 'primary';

      // If this is a composite ID (contains underscore), parse it
      if (compositeId.includes('_')) {
        const parts = compositeId.split('_');
        if (parts.length >= 2) {
          accountId = parts[0];
          emailId = parts.slice(1).join('_'); // In case the email ID itself contains underscores
        }
      }

      return {
        id: doc.id,
        emailId: emailId || doc.id,
        accountId: accountId,
        compositeId: compositeId,
        ...data,
        // Ensure we have the correct field mappings
        isOrder: data.is_order || data.isOrder || false,
        isInvoice: data.is_invoice || data.isInvoice || false,
        orderNumber: data.order_number || data.orderNumber || '',
        totalAmount: data.total_amount || data.totalAmount || '',
        analyzedAt: data.analyzed_at || data.analyzedAt || new Date().toISOString(),
        summary: data.summary || (data.analysis ? data.analysis.summary : ''),
        // Include the full analysis data
        analysis: data.analysis || data
      } as unknown as EmailAnalysisData;
    });
  } catch (error) {
    console.error('Error getting all email analyses:', error);
    return []; // Return empty array instead of throwing to avoid breaking the analysis flow
  }
};

// Get a specific email analysis by email ID and account ID
export const getEmailAnalysisByEmailId = async (emailId: string, accountId: string = 'primary') => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Create a composite ID that includes the account ID
    const compositeId = createCompositeId(emailId, accountId);
    console.log(`Looking for email analysis with composite ID: ${compositeId}`);

    // Use the correct path: /users/{firebase_user_id}/email_analyses/{composite_id}
    const emailAnalysisDocRef = doc(db, 'users', user.uid, 'email_analyses', compositeId);
    const docSnapshot = await getDocWithRetry(emailAnalysisDocRef);

    // If not found with composite ID, try the original email ID as fallback for backward compatibility
    if (!docSnapshot.exists()) {
      console.log(`No email analysis found with composite ID, trying original email ID: ${emailId}`);
      const legacyDocRef = doc(db, 'users', user.uid, 'email_analyses', emailId);
      const legacySnapshot = await getDocWithRetry(legacyDocRef);

      if (legacySnapshot.exists()) {
        console.log(`Found email analysis with legacy ID: ${emailId}`);
        return processAnalysisData(legacySnapshot);
      }
    }

    if (!docSnapshot.exists()) {
      console.log(`No email analysis found for email ID: ${emailId}`);
      return null;
    }

    const data = docSnapshot.data();
    console.log(`Found email analysis for email ID: ${emailId}`);

    return processAnalysisData(docSnapshot);
  } catch (error) {
    console.error('Error getting email analysis:', error);
    throw error;
  }
};

// Helper function to process analysis data from a document snapshot
const processAnalysisData = (docSnapshot: any) => {
  const data = docSnapshot.data();

  // Extract account_id and email_id from composite_id if present
  const compositeId = docSnapshot.id;
  let emailId = data.email_id || data.emailId || '';
  let accountId = data.account_id || data.accountId || 'primary';

  // If this is a composite ID (contains underscore), parse it
  if (compositeId.includes('_')) {
    const parts = compositeId.split('_');
    if (parts.length >= 2) {
      accountId = parts[0];
      emailId = parts.slice(1).join('_'); // In case the email ID itself contains underscores
    }
  }

  return {
    id: docSnapshot.id,
    emailId: emailId || docSnapshot.id,
    accountId: accountId,
    compositeId: compositeId,
    ...data,
    // Ensure we have the correct field mappings
    isOrder: data.is_order || data.isOrder || false,
    isInvoice: data.is_invoice || data.isInvoice || false,
    orderNumber: data.order_number || data.orderNumber || '',
    totalAmount: data.total_amount || data.totalAmount || '',
    analyzedAt: data.analyzed_at || data.analyzedAt || new Date().toISOString(),
    summary: data.summary || (data.analysis ? data.analysis.summary : ''),
    // Include the full analysis data
    analysis: data.analysis || data
  };
};

/**
 * Sets up a real-time listener for email analyses in Firestore
 * This function will call the provided callback whenever new analyses are added or existing ones are updated
 */
export const setupEmailAnalysisListener = (callback: (analyses: EmailAnalysisData[]) => void, accountId?: string) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error('No authenticated user found when trying to set up email analysis listener');
      return () => {}; // Return empty unsubscribe function
    }

    console.log(`Setting up real-time listener for email analyses${accountId ? ` for account ${accountId}` : ''}`);

    // Reference to the email analyses collection for this user
    const analysesRef = collection(db, 'users', user.uid, 'email_analyses');

    // Create query based on parameters
    let analysesQuery;

    if (accountId) {
      // Filter by accountId if provided - use both field names for compatibility
      analysesQuery = query(
        analysesRef,
        where('accountId', '==', accountId),
        orderBy('analyzed_at', 'desc')
      );
    } else {
      // Get all analyses if no accountId filter
      analysesQuery = query(
        analysesRef,
        orderBy('analyzed_at', 'desc')
      );
    }

    // Set up the real-time listener
    const unsubscribe = onSnapshot(analysesQuery, (querySnapshot) => {
      console.log(`Real-time update: ${querySnapshot.docs.length} email analyses`);

      // Process all results and include ALL fields from Firestore
      const results = querySnapshot.docs.map(doc => {
        const data = doc.data();
        const analysisData = data.analysis || data;

        return {
          id: doc.id,
          emailId: data.emailId || data.email_id || doc.id,
          accountId: data.accountId || data.account_id || 'primary',
          subject: data.subject || analysisData.subject || '',
          from: data.from || analysisData.from || '',
          to: data.to || analysisData.to || '',
          date: data.date || analysisData.date || '',
          summary: data.summary || (analysisData.summary) || '',
          category: data.category || analysisData.category || 'other',
          sentiment: data.sentiment || analysisData.sentiment || 'neutral',
          keyDetails: data.keyDetails || data.key_details || analysisData.keyDetails || analysisData.key_details || {},
          keyDates: data.keyDates || data.key_dates || analysisData.keyDates || analysisData.key_dates || [],
          actionItems: data.actionItems || data.action_items || analysisData.actionItems || analysisData.action_items || [],
          isOrder: data.isOrder || data.is_order || analysisData.isOrder || analysisData.is_order || false,
          isInvoice: data.isInvoice || data.is_invoice || analysisData.isInvoice || analysisData.is_invoice || false,
          orderNumber: data.orderNumber || data.order_number || analysisData.orderNumber || analysisData.order_number || '',
          totalAmount: data.totalAmount || data.total_amount || analysisData.totalAmount || analysisData.total_amount || '',
          analyzedAt: data.analyzedAt || data.analyzed_at || analysisData.analyzedAt || analysisData.analyzed_at || new Date().toISOString(),
          // Include the full analysis data
          analysis: analysisData
        } as unknown as EmailAnalysisData;
      });

      // Call the callback with the updated results
      callback(results);
    }, (error) => {
      console.error('Error in email analysis listener:', error);
    });

    // Return the unsubscribe function
    return unsubscribe;
  } catch (error) {
    console.error('Error setting up email analysis listener:', error);
    return () => {}; // Return empty unsubscribe function
  }
};