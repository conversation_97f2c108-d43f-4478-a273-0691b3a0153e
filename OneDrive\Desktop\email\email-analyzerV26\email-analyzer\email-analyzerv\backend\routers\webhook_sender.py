"""
Updated Webhook Sender Module with Clean JSON Structure

This module handles sending email analysis data and attachments to a webhook
using the new clean, non-duplicate JSON structure.
"""

import requests
import logging
import json
import base64
import tempfile
import os
from typing import Dict, Any, List, Optional, Union
from firebase_admin import storage
import time
import traceback
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("webhook_sender")
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)
logger.propagate = True

# Webhook URL
WEBHOOK_URL = "https://spectrum.omsflow.com/api/po_webhook.php"

# Deduplication tracker
webhook_call_tracker = {}

def format_date_to_string(date_value: Any) -> str:
    """
    Convert various date formats to YYYY-MM-DD string format
    """
    if not date_value:
        return ""
    
    try:
        if isinstance(date_value, str):
            if len(date_value) == 10 and date_value.count('-') == 2:
                try:
                    datetime.strptime(date_value, '%Y-%m-%d')
                    return date_value
                except ValueError:
                    pass
            
            # Try to parse common date formats
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M:%S.%f']:
                try:
                    parsed_date = datetime.strptime(date_value, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        elif isinstance(date_value, datetime):
            return date_value.strftime('%Y-%m-%d')
        
        elif isinstance(date_value, (int, float)):
            return datetime.fromtimestamp(date_value).strftime('%Y-%m-%d')
        
        print(f"[DATE FORMAT] Could not parse date: {date_value} (type: {type(date_value)})")
        return ""
        
    except Exception as e:
        print(f"[DATE FORMAT ERROR] Error formatting date {date_value}: {str(e)}")
        return ""

def convert_attachment_to_blob(attachment_data: bytes) -> str:
    """Convert attachment data to base64 encoded string"""
    try:
        blob_data = base64.b64encode(attachment_data).decode('utf-8')
        print(f"[WEBHOOK DATA] Converted attachment to blob, size: {len(blob_data)} characters")
        return blob_data
    except Exception as e:
        logger.error(f"Error converting attachment to blob: {str(e)}")
        return ""

def download_attachment_from_url(url: str) -> Optional[bytes]:
    """Download attachment data from a URL"""
    try:
        print(f"[WEBHOOK DATA] Downloading attachment from URL: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            print(f"[WEBHOOK DATA] Successfully downloaded attachment, size: {len(response.content)} bytes")
            return response.content
        else:
            logger.error(f"Failed to download attachment from URL: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading attachment from URL: {str(e)}")
        return None

def extract_document_number(analysis_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Extract document numbers (PO, Invoice, Order) from analysis data
    """
    document_numbers = {
        "document_number": "",
        "po_number": "",
        "invoice_number": ""
    }
    
    try:
        # Check various locations for document numbers
        if 'analysis' in analysis_data:
            analysis = analysis_data['analysis']
            doc_info = analysis.get('document_info', {})
        else:
            doc_info = analysis_data.get('document_info', {})
        
        # Extract PO number
        po_sources = [
            doc_info.get('po_number'),
            doc_info.get('order_number'),
            analysis_data.get('po_number'),
            analysis_data.get('order_number')
        ]
        
        for po in po_sources:
            if po and str(po).strip() and str(po).strip().lower() not in ['null', 'none', '']:
                document_numbers["po_number"] = str(po).strip()
                if not document_numbers["document_number"]:
                    document_numbers["document_number"] = str(po).strip()
                break
        
        # Extract Invoice number
        invoice_sources = [
            doc_info.get('invoice_number'),
            analysis_data.get('invoice_number')
        ]
        
        for invoice in invoice_sources:
            if invoice and str(invoice).strip() and str(invoice).strip().lower() not in ['null', 'none', '']:
                document_numbers["invoice_number"] = str(invoice).strip()
                if not document_numbers["document_number"]:
                    document_numbers["document_number"] = str(invoice).strip()
                break
        
        print(f"[DOCUMENT EXTRACTION] Extracted numbers: {document_numbers}")
        return document_numbers
        
    except Exception as e:
        logger.error(f"Error extracting document numbers: {str(e)}")
        return document_numbers

def prepare_webhook_payload(analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare the payload using the new clean JSON structure
    """
    try:
        print(f"\n[WEBHOOK PREP] Starting payload preparation with clean structure...")
        
        # Extract nested analysis data
        if 'analysis' in analysis_data:
            analysis = analysis_data['analysis']
            email_analysis = analysis.get('email_analysis', {})
            attachment_analysis = analysis.get('attachment_analysis', [])
            financial_details = analysis.get('financial_details', {})
            document_info = analysis.get('document_info', {})
            parties = analysis.get('parties', {})
            items = analysis.get('items', [])
            dates = analysis.get('dates', {})
            additional_info = analysis.get('additional_info', {})
            order_details = analysis.get('order_details', {})
            payment_info = analysis.get('payment_info', {})
        else:
            email_analysis = analysis_data.get('email_analysis', {})
            attachment_analysis = analysis_data.get('attachment_analysis', [])
            financial_details = analysis_data.get('financial_details', {})
            document_info = analysis_data.get('document_info', {})
            parties = analysis_data.get('parties', {})
            items = analysis_data.get('items', [])
            dates = analysis_data.get('dates', {})
            additional_info = analysis_data.get('additional_info', {})
            order_details = analysis_data.get('order_details', {})
            payment_info = analysis_data.get('payment_info', {})
        
        # Extract document numbers
        doc_numbers = extract_document_number(analysis_data)
        
        # Determine document type and category
        category = analysis_data.get('category', email_analysis.get('category', 'other'))
        if category == 'order':
            category = 'purchase_order'
        
        document_type = document_info.get('type', 'Document')
        if 'order' in category.lower() or 'purchase' in category.lower():
            document_type = 'Purchase Order'
        elif 'invoice' in category.lower():
            document_type = 'Invoice'
        
        # Build clean payload structure
        payload = {
            "company_id": "1006",
            "created_by": "1",
            "email_id": analysis_data.get('email_id', ''),
            "category": category,
            "analyzed_at": format_date_to_string(analysis_data.get('analyzed_at', datetime.now().isoformat())),
            
            "document_info": {
                "type": document_type,
                "document_number": doc_numbers["document_number"],
                "po_number": doc_numbers["po_number"],
                "invoice_number": doc_numbers["invoice_number"],
                "reference_numbers": document_info.get('reference_numbers', []),
                "status": document_info.get('status', 'pending')
            },
            
            "email_metadata": {
                "subject": analysis_data.get('subject', ''),
                "from": analysis_data.get('from', ''),
                "to": analysis_data.get('to', ''),
                "cc": analysis_data.get('cc', ''),
                "timestamp": format_date_to_string(analysis_data.get('date', '')),
                "has_attachments": bool(analysis_data.get('attachments', [])),
                "attachment_count": len(analysis_data.get('attachments', [])),
                "attachment_names": [att.get('filename', '') for att in analysis_data.get('attachments', [])]
            },
            
            "parties": {
                "vendor": {
                    "name": parties.get('vendor', {}).get('name', ''),
                    "address": parties.get('vendor', {}).get('address', ''),
                    "phone": parties.get('vendor', {}).get('phone', ''),
                    "email": parties.get('vendor', {}).get('email', ''),
                    "tax_id": parties.get('vendor', {}).get('tax_id', '')
                },
                "customer": {
                    "name": parties.get('customer', {}).get('name', ''),
                    "address": parties.get('customer', {}).get('address', ''),
                    "phone": parties.get('customer', {}).get('phone', ''),
                    "email": parties.get('customer', {}).get('email', ''),
                    "customer_id": parties.get('customer', {}).get('customer_id', '')
                },
                "ship_to": {
                    "name": parties.get('ship_to', {}).get('name', ''),
                    "address": parties.get('ship_to', {}).get('address', ''),
                    "phone": parties.get('ship_to', {}).get('phone', '')
                }
            },
            
            "dates": {
                "document_date": format_date_to_string(dates.get('invoice_date') or dates.get('order_date')),
                "due_date": format_date_to_string(dates.get('due_date')),
                "ship_date": format_date_to_string(dates.get('ship_date')),
                "delivery_date": format_date_to_string(dates.get('delivery_date')),
                "payment_date": format_date_to_string(dates.get('payment_date'))
            },
            
            "financial_details": {
                "currency": financial_details.get('currency', 'USD'),
                "subtotal": str(financial_details.get('subtotal', '0.00')),
                "tax_total": str(financial_details.get('total_tax', '0.00')),
                "shipping_cost": str(financial_details.get('shipping', '0.00')),
                "total_amount": str(financial_details.get('total_amount', analysis_data.get('total_amount', '0.00'))),
                "amount_paid": str(financial_details.get('amount_paid', '0.00')),
                "balance_due": str(financial_details.get('balance_due', financial_details.get('total_amount', '0.00'))),
                "payment_terms": payment_info.get('terms', 'Net 30')
            },
            
            "items": [
                {
                    "item_number": item.get('item_number', ''),
                    "description": item.get('description', ''),
                    "quantity": item.get('qty', item.get('quantity', 0)),
                    "unit": item.get('unit', 'EA'),
                    "unit_price": str(item.get('rate', item.get('unit_price', '0.00'))),
                    "line_total": str(item.get('total', item.get('line_total', '0.00'))),
                    "tax_rate": str(item.get('tax_rate', '0.00')),
                    "attributes": item.get('attributes', {})
                }
                for item in items
            ],
            
            "order_details": {
                "delivery_method": order_details.get('delivery_method', ''),
                "carrier": order_details.get('carrier', ''),
                "tracking_number": order_details.get('tracking_number', ''),
                "shipping_terms": order_details.get('shipping_terms', ''),
                "estimated_delivery": format_date_to_string(order_details.get('estimated_arrival')),
                "special_instructions": order_details.get('delivery_instructions', '')
            },
            
            "payment_info": {
                "payment_method": payment_info.get('method', ''),
                "payment_terms": payment_info.get('terms', 'Net 30'),
                "early_payment_discount": payment_info.get('early_payment_discount', ''),
                "late_payment_penalty": payment_info.get('late_payment_penalty', '')
            },
            
            "analysis_results": {
                "summary": analysis_data.get('summary', email_analysis.get('summary', 'No summary generated')),
                "sentiment": email_analysis.get('sentiment', 'neutral'),
                "confidence_score": 0.95,  # Default confidence score
                "key_dates_extracted": len([d for d in payload["dates"].values() if d]),
                "action_items": email_analysis.get('action_items', analysis_data.get('action_items', []))
            },
            
            "attachments": [],
            
            "additional_info": {
                "terms_conditions": additional_info.get('terms_conditions', ''),
                "return_policy": additional_info.get('return_policy', ''),
                "warranty": additional_info.get('warranty', ''),
                "notes": additional_info.get('notes', '')
            },
            
            "system_metadata": {
                "processed_at": datetime.now().isoformat(),
                "processing_version": "2.1",
                "confidence_score": 0.95,
                "extraction_method": "gemini_ai_multimodal"
            }
        }
        
        # Process attachments with blob data
        processed_attachments = []
        
        # Use attachment_analysis if available, otherwise use original attachments
        attachments_to_process = attachment_analysis if attachment_analysis else analysis_data.get('attachments', [])
        
        for attachment in attachments_to_process:
            attachment_entry = {
                "filename": attachment.get('filename', ''),
                "content_type": attachment.get('content_type', attachment.get('mimeType', 'application/octet-stream')),
                "size": attachment.get('size', 0),
                "url": attachment.get('url', ''),
                "viewer_url": "",
                "blob_data": "",
                "analysis": {
                    "document_type": attachment.get('type', 'Document'),
                    "extracted_data_confidence": 0.98,
                    "content_summary": attachment.get('content_summary', f"Attachment: {attachment.get('filename', 'unknown')}")
                }
            }
            
            # Download and convert to blob if URL is available
            if attachment.get('url'):
                # Add Google Document Viewer URL for PDFs and docs
                content_type = attachment_entry["content_type"].lower()
                if content_type in ['application/pdf', 'application/msword',
                                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                    attachment_entry["viewer_url"] = f"https://docs.google.com/viewer?url={attachment['url']}&embedded=true"
                
                # Download and convert to blob
                attachment_data = download_attachment_from_url(attachment['url'])
                if attachment_data:
                    attachment_entry["blob_data"] = convert_attachment_to_blob(attachment_data)
                    attachment_entry["size"] = len(attachment_data)
                    print(f"[WEBHOOK PREP] Successfully processed attachment: {attachment.get('filename', 'unnamed')}")
                else:
                    print(f"[WEBHOOK PREP] Failed to download attachment: {attachment.get('filename', 'unnamed')}")
            
            processed_attachments.append(attachment_entry)
        
        payload["attachments"] = processed_attachments
        
        # Final logging
        print(f"[WEBHOOK PREP] Clean payload prepared successfully")
        print(f"[WEBHOOK PREP] Category: {payload['category']}")
        print(f"[WEBHOOK PREP] Document type: {payload['document_info']['type']}")
        print(f"[WEBHOOK PREP] Document number: {payload['document_info']['document_number']}")
        print(f"[WEBHOOK PREP] Items count: {len(payload['items'])}")
        print(f"[WEBHOOK PREP] Attachments count: {len(payload['attachments'])}")
        print(f"[WEBHOOK PREP] Total amount: {payload['financial_details']['total_amount']}")
        
        return payload
        
    except Exception as e:
        logger.error(f"Error preparing clean webhook payload: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"[WEBHOOK ERROR] Failed to prepare clean payload: {str(e)}")
        return analysis_data

def send_to_webhook(analysis_data: Dict[str, Any]) -> bool:
    """
    Send email analysis data to webhook using clean JSON structure
    """
    global webhook_call_tracker
    
    print(f"\n[WEBHOOK CALL] =========================")
    print(f"[WEBHOOK CALL] Function called with clean structure")
    print(f"[WEBHOOK CALL] =========================")
    
    try:
        # Create deduplication key
        email_id = analysis_data.get('email_id', '')
        email_fingerprint = analysis_data.get('email_fingerprint', '')
        webhook_key = f"{email_id}_{email_fingerprint}"
        
        # Check for duplicates
        current_time = time.time()
        if webhook_key in webhook_call_tracker:
            last_call_time = webhook_call_tracker[webhook_key]
            if current_time - last_call_time < 60:  # 60 seconds
                print(f"[WEBHOOK SKIP] Skipping duplicate webhook call for {webhook_key}")
                return True
        
        webhook_call_tracker[webhook_key] = current_time
        
        # Clean up old tracker entries
        cutoff_time = current_time - 600
        webhook_call_tracker = {k: v for k, v in webhook_call_tracker.items() if v > cutoff_time}
        
        # Prepare clean payload
        print(f"[WEBHOOK CALL] Preparing clean payload...")
        payload = prepare_webhook_payload(analysis_data)
        
        # Convert to JSON
        json_payload = json.dumps(payload, indent=2)
        
        print(f"\n[WEBHOOK JSON] =========================")
        print(f"[WEBHOOK JSON] CLEAN JSON PAYLOAD")
        print(f"[WEBHOOK JSON] =========================")
        print(json_payload)
        print(f"[WEBHOOK JSON] =========================")
        print(f"[WEBHOOK JSON] END CLEAN JSON PAYLOAD")
        print(f"[WEBHOOK JSON] =========================\n")
        
        # Send request
        headers = {'Content-Type': 'application/json'}
        print(f"[WEBHOOK SEND] Sending to: {WEBHOOK_URL}")
        
        response = requests.post(WEBHOOK_URL, data=json_payload, headers=headers, timeout=60)
        
        print(f"[WEBHOOK RESPONSE] Status Code: {response.status_code}")
        print(f"[WEBHOOK RESPONSE] Body: {response.text}")
        
        if 200 <= response.status_code < 300:
            print(f"[WEBHOOK SUCCESS] Clean payload sent successfully!")
            logger.info(f"WEBHOOK SUCCESS: Clean structure data sent to {WEBHOOK_URL} - Status {response.status_code}")
            
            try:
                response_json = response.json()
                print(f"[WEBHOOK SUCCESS] Response JSON: {response_json}")
                if response_json.get('status') == 'success':
                    print(f"[WEBHOOK SUCCESS] Data accepted - Quote ID: {response_json.get('quote_id', 'not provided')}")
            except:
                print(f"[WEBHOOK NOTE] Response was not valid JSON")
            
            return True
        else:
            print(f"[WEBHOOK ERROR] Failed to send clean payload - Status: {response.status_code}")
            logger.error(f"WEBHOOK ERROR: Failed to send clean data - Status: {response.status_code}, Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"WEBHOOK ERROR: Exception in clean webhook sender: {str(e)}")
        logger.error(f"WEBHOOK ERROR: Traceback: {traceback.format_exc()}")
        print(f"[WEBHOOK ERROR] Exception: {str(e)}")
        return False

# Keep existing utility functions
def clear_webhook_tracker():
    """Clear the webhook call tracker"""
    global webhook_call_tracker
    old_count = len(webhook_call_tracker)
    webhook_call_tracker.clear()
    print(f"[UTILITY] Cleared webhook tracker ({old_count} entries removed)")

def get_webhook_tracker_status():
    """Get current webhook tracker status"""
    print(f"[UTILITY] Webhook tracker status:")
    print(f"[UTILITY] Total tracked calls: {len(webhook_call_tracker)}")
    current_time = time.time()
    for key, timestamp in webhook_call_tracker.items():
        age = current_time - timestamp
        print(f"[UTILITY] {key}: {age:.1f} seconds ago")

def test_webhook_with_clean_structure():
    """Test webhook with clean structure"""
    sample_data = {
        "company_id": "1006",
        "created_by": "1",
        "email_id": "test_email_clean_123",
        "category": "purchase_order",
        "subject": "Test Clean PO Email",
        "from": "<EMAIL>",
        "to": "<EMAIL>",
        "date": "2025-05-28",
        "total_amount": "1000.00",
        "analysis": {
            "document_info": {
                "type": "Purchase Order",
                "po_number": "PO-CLEAN-001"
            },
            "financial_details": {
                "total_amount": "1000.00",
                "subtotal": "900.00",
                "total_tax": "100.00"
            },
            "items": [
                {
                    "item_number": "CLEAN-001",
                    "description": "Test Clean Item",
                    "qty": 1,
                    "rate": "900.00",
                    "total": "900.00"
                }
            ],
            "email_analysis": {
                "summary": "Clean test purchase order",
                "sentiment": "neutral"
            }
        },
        "attachments": []
    }
    
    print(f"[TEST] Testing clean webhook structure...")
    result = send_to_webhook(sample_data)
    print(f"[TEST] Clean webhook test result: {'SUCCESS' if result else 'FAILED'}")
    return result

if __name__ == "__main__":
    print("Testing clean webhook sender functionality...")
    test_webhook_with_clean_structure()w